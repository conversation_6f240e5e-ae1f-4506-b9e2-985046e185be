#!/usr/bin/env python3
"""
Create test cache data for testing cache functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import save_to_cache, fetch_stock_data, normalize_to_day_of_year
from datetime import datetime

def create_test_cache_data():
    """Create some test cache data"""
    print("Creating Test Cache Data")
    print("=" * 40)
    
    # Test configurations
    test_configs = [
        {
            'ticker': 'AAPL',
            'num_years': 3,
            'start_month': 1,
            'start_day': 1,
            'end_month': 12,
            'end_day': 31
        },
        {
            'ticker': 'MSFT', 
            'num_years': 2,
            'start_month': 6,
            'start_day': 1,
            'end_month': 11,
            'end_day': 30
        },
        {
            'ticker': 'GOOGL',
            'num_years': 4,
            'start_month': 3,
            'start_day': 15,
            'end_month': 9,
            'end_day': 15
        }
    ]
    
    for config in test_configs:
        ticker = config['ticker']
        num_years = config['num_years']
        start_month = config['start_month']
        start_day = config['start_day']
        end_month = config['end_month']
        end_day = config['end_day']
        
        print(f"\nCreating cache for {ticker} ({num_years} years)...")
        
        # Fetch real data for this ticker
        current_year = datetime.now().year
        years_data = {}
        
        for i in range(num_years):
            year = current_year - i
            
            try:
                start_date = datetime(year, start_month, start_day)
                end_date = datetime(year, end_month, end_day)
                
                # Handle year wrap-around
                if end_date < start_date:
                    end_date = datetime(year + 1, end_month, end_day)
                    
            except ValueError:
                # Handle invalid dates (like Feb 30)
                start_date = datetime(year, start_month, min(start_day, 28))
                end_date = datetime(year, end_month, min(end_day, 28))
                if end_date < start_date:
                    end_date = datetime(year + 1, end_month, min(end_day, 28))
            
            print(f"  Fetching {year} data ({start_date.strftime('%m/%d')} - {end_date.strftime('%m/%d')})...")
            
            # Fetch data for this year
            data = fetch_stock_data(ticker, start_date, end_date)
            
            if data is not None and not data.empty:
                # Normalize to day of year
                normalized_data = normalize_to_day_of_year(data, year)
                years_data[year] = normalized_data
                print(f"    ✅ {len(data)} days loaded")
            else:
                print(f"    ❌ No data for {year}")
        
        if years_data:
            # Save to cache
            try:
                save_to_cache(
                    ticker,
                    num_years,
                    start_month,
                    start_day,
                    end_month,
                    end_day,
                    years_data
                )
                print(f"  ✅ Cached {ticker} with {len(years_data)} years")
            except Exception as e:
                print(f"  ❌ Cache save failed: {e}")
        else:
            print(f"  ❌ No data to cache for {ticker}")
    
    print(f"\n✅ Test cache creation complete!")

def verify_cache_data():
    """Verify the created cache data"""
    print("\nVerifying Cache Data")
    print("=" * 40)
    
    from app import get_all_cached_entries, load_from_cache
    
    # Get all cached entries
    cached_entries = get_all_cached_entries()
    print(f"Found {len(cached_entries)} cached entries")
    
    for i, entry in enumerate(cached_entries):
        print(f"\nEntry {i+1}: {entry['ticker']} ({entry['num_years']}y)")
        print(f"  Date range: {entry['start_month']}/{entry['start_day']} - {entry['end_month']}/{entry['end_day']}")
        
        # Try to load the data
        try:
            cached_data = load_from_cache(
                entry['ticker'],
                entry['num_years'],
                entry['start_month'],
                entry['start_day'],
                entry['end_month'],
                entry['end_day']
            )
            
            if cached_data:
                years_data = cached_data['years_data']
                print(f"  ✅ Loadable with {len(years_data)} years: {list(years_data.keys())}")
                
                # Check data structure
                for year, data in years_data.items():
                    if data is not None and hasattr(data, '__len__'):
                        print(f"    {year}: {len(data)} data points")
                    else:
                        print(f"    {year}: Invalid data structure")
            else:
                print(f"  ❌ Failed to load cached data")
                
        except Exception as e:
            print(f"  ❌ Load error: {e}")

def main():
    """Create and verify test cache data"""
    print("Test Cache Data Creation")
    print("=" * 50)
    
    create_test_cache_data()
    verify_cache_data()
    
    print("\n" + "=" * 50)
    print("✅ Test Cache Data Ready!")
    print("=" * 50)
    
    print("\nNext Steps:")
    print("1. Run the Streamlit app: streamlit run app.py")
    print("2. Go to 'Seasonal Stock Analysis' page")
    print("3. Look for cached analyses in the sidebar")
    print("4. Click on any cached analysis button")
    print("5. Graphics should load automatically!")
    
    print("\nExpected Behavior:")
    print("• Cached analysis buttons appear in sidebar")
    print("• Clicking button loads graphics immediately")
    print("• Form controls update to show cached parameters")
    print("• No need to click 'Analyze' button manually")

if __name__ == "__main__":
    main()
