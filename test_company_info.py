#!/usr/bin/env python3
"""
Test script for the company information feature
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import get_company_info

def test_company_info():
    """Test the company information function"""
    print("Testing Company Information Feature")
    print("=" * 60)
    
    # Test major tickers
    test_tickers = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'AMZN']
    
    for ticker in test_tickers:
        print(f"\n🏢 {ticker} Company Information:")
        print("-" * 50)
        
        info = get_company_info(ticker)
        
        print(f"Name: {info['name']}")
        print(f"Industry: {info['industry']}")
        print(f"Sector: {info['sector']}")
        print(f"Market Cap: {info['market_cap']}")
        print(f"Employees: {info['employees']}")
        print(f"Founded: {info['founded']}")
        print(f"Headquarters: {info['headquarters']}")
        print(f"\nDescription: {info['description'][:100]}...")
        print(f"\nKey Products: {info['key_products']}")
        
        # Analyst predictions
        pred = info['analyst_predictions']
        print(f"\nAnalyst Outlook:")
        print(f"  Price Target: {pred['price_target']}")
        print(f"  Rating: {pred['rating']}")
        print(f"  Growth Outlook: {pred['growth_outlook'][:80]}...")
        print(f"  Key Catalysts: {pred['key_catalysts'][:80]}...")
        print(f"  Key Risks: {pred['risks'][:80]}...")
    
    # Test unknown ticker
    print(f"\n🏢 UNKNOWN Ticker (XYZ):")
    print("-" * 50)
    info = get_company_info("XYZ")
    print(f"Name: {info['name']}")
    print(f"Industry: {info['industry']}")
    print(f"Description: {info['description']}")
    
    print("\n" + "=" * 60)
    print("✅ Company information feature working correctly!")
    
    print("\nFeatures included:")
    print("- Company name, industry, and sector")
    print("- Detailed business description")
    print("- Key financial metrics (market cap, employees)")
    print("- Company facts (founded, headquarters)")
    print("- Key products and revenue breakdown")
    print("- Analyst price targets and ratings")
    print("- Growth outlook and predictions")
    print("- Key business catalysts")
    print("- Investment risks and challenges")
    print("- Fallback information for unknown tickers")

if __name__ == "__main__":
    test_company_info()
