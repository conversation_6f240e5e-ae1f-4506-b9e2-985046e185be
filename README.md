# Seasonal Stock Analysis

A Streamlit application that compares seasonal stock price movements across different years to identify patterns and trends.

## Features

- **Interactive Stock Analysis**: Enter any stock ticker symbol to analyze
- **Flexible Time Periods**: Choose custom seasonal date ranges (e.g., November to February)
- **Multi-Year Comparison**: Compare up to 10 years of seasonal data
- **Triple Chart Display**:
  - **Price Chart**: Shows actual stock prices with optional logarithmic scale
  - **Return Chart**: Shows cumulative percentage returns from period start
  - **Recent Comparison**: Focuses on last year vs this year for detailed analysis
- **Smooth Monthly Average Lines**: Thick gray lines show smoothed monthly average trends
- **Distinct Color Scheme**: Each year gets a unique, easily distinguishable color
- **Precise Hover Tooltips**: Shows information only for the specific line you're hovering over
- **Ticker-Specific Context**: Summary table includes company-specific events and volatility drivers
- **Logarithmic Scale Option**: Better for comparing percentage changes across different price levels
- **Enhanced Statistics**: Returns, volatility, max drawdown, and price ranges for each year
- **Company-Specific Events**: Detailed earnings, product launches, and business developments for each ticker
- **Reliable Data Sources**: Multiple fallback methods ensure data availability
- **Demo Mode**: Use "DEMO" ticker for testing with realistic seasonal patterns

## Installation

1. Clone or download this repository
2. Create a virtual environment:
   ```bash
   python -m venv .venv
   ```

3. Activate the virtual environment:
   - Windows: `.venv\Scripts\activate`
   - macOS/Linux: `source .venv/bin/activate`

4. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Option 1: Using the batch file (Windows)
1. Double-click `run_app.bat` to start the application

### Option 2: Manual startup
1. Activate the virtual environment:
   - Windows: `.venv\Scripts\activate`
   - macOS/Linux: `source .venv/bin/activate`

2. Run the Streamlit application:
   ```bash
   streamlit run app.py
   ```

3. Open your web browser and navigate to the provided URL (typically `http://localhost:8501`)

### Using the Application
1. Use the sidebar to configure your analysis:
   - Enter a stock ticker symbol (e.g., AAPL, MSFT, GOOGL) or click "DEMO" for test data
   - Select the number of years to compare (2-10 years)
   - Choose your seasonal date range (start and end months/days)
   - Optionally enable logarithmic scale for the price chart
   - Click "Analyze Seasonal Patterns"

2. The app will:
   - Fetch historical stock data for each year using reliable data sources
   - Display three interactive charts:
     - **Price Chart**: Actual stock prices (with optional log scale)
     - **Return Chart**: Cumulative percentage returns from period start
     - **Recent Comparison**: Direct comparison of last year vs this year
   - Show enhanced summary statistics including max drawdown and price ranges

## Example Use Cases

- **Holiday Season Analysis**: Compare November-January performance across years
- **Summer Trading Patterns**: Analyze May-August movements ("sell in May and go away")
- **Earnings Season Effects**: Look at quarterly patterns around earnings announcements
- **Tax Season Impact**: Study January-April movements (tax loss harvesting effects)
- **Back-to-School Season**: Analyze August-September patterns for retail/education stocks
- **Year-End Effects**: Compare December performance patterns

## Chart Types Explained

### Price Chart
- Shows actual stock prices over the seasonal period
- **Linear Scale**: Good for absolute price comparisons
- **Logarithmic Scale**: Better for percentage change comparisons, especially when:
  - Stock prices have changed significantly over the years
  - Comparing stocks with very different price levels
  - Focusing on percentage movements rather than absolute dollar changes

### Return Chart
- Shows cumulative percentage returns from the start of each seasonal period
- All years start at 0% for easy comparison
- Horizontal dashed line at 0% shows break-even point
- Better for comparing relative performance across years

### Recent Comparison Chart
- Focuses specifically on last year vs this year
- Uses bold, contrasting colors (red vs blue)
- Thicker lines for better visibility
- Perfect for analyzing current year performance against recent history
- Helps identify if this year is following typical seasonal patterns

## Interactive Features

### Hover Behavior
- **Precise Tooltips**: Hover shows data only for the specific line you're pointing at
- **No Clutter**: Unlike unified hover modes, you see exactly what you need
- **Clear Information**: Year, day, price/return clearly displayed

### Color Scheme
- **Distinct Colors**: Each year gets a unique, easily distinguishable color
- **No Confusion**: No similar shades that are hard to tell apart
- **Consistent**: Same color for the same year across all charts

## Dependencies

- `streamlit`: Web application framework
- `yfinance`: Yahoo Finance API for stock data
- `pandas`: Data manipulation and analysis
- `plotly`: Interactive charting library

## Notes

- Stock data is fetched from Yahoo Finance
- All prices are normalized to start at 100 for easy comparison
- The application handles invalid dates and missing data gracefully
- Volatility is calculated as annualized standard deviation of daily returns
