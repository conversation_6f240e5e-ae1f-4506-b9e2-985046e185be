#!/usr/bin/env python3
"""
Test script for Multi-Ticker functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_multi_ticker_price_chart, create_multi_ticker_rsi_chart, fetch_stock_data, normalize_to_day_of_year
from datetime import datetime

def test_multi_ticker_charts():
    """Test multi-ticker chart creation"""
    print("Testing Multi-Ticker Chart Creation")
    print("=" * 50)
    
    # Test tickers
    tickers = ['AAPL', 'MSFT', 'GOOGL']
    num_years = 2
    
    print(f"Testing with tickers: {', '.join(tickers)}")
    print(f"Analysis period: {num_years} years")
    
    # Fetch data for all tickers
    all_years_data = {}
    current_year = datetime.now().year
    
    for ticker in tickers:
        print(f"\nFetching data for {ticker}...")
        years_data = {}
        
        for i in range(num_years):
            year = current_year - i
            start_date = datetime(year, 1, 1)
            end_date = datetime(year, 12, 31)
            
            # Fetch data for this year
            data = fetch_stock_data(ticker, start_date, end_date)
            
            if data is not None and not data.empty:
                normalized_data = normalize_to_day_of_year(data, year)
                years_data[year] = normalized_data
                print(f"  ✅ {year}: {len(data)} days of data")
            else:
                print(f"  ❌ {year}: No data")
        
        if years_data:
            all_years_data[ticker] = years_data
            print(f"  Total: {len(years_data)} years loaded for {ticker}")
        else:
            print(f"  Failed to load any data for {ticker}")
    
    successful_tickers = list(all_years_data.keys())
    print(f"\nSuccessfully loaded data for: {', '.join(successful_tickers)}")
    
    if successful_tickers:
        # Test price chart creation
        print(f"\nCreating multi-ticker price chart...")
        try:
            price_fig = create_multi_ticker_price_chart(successful_tickers, all_years_data, num_years)
            if price_fig:
                print(f"✅ Price chart created successfully")
                print(f"  Chart title: {price_fig.layout.title.text}")
                print(f"  Number of traces: {len(price_fig.data)}")
                
                # Check traces
                for i, trace in enumerate(price_fig.data):
                    print(f"  Trace {i+1}: {trace.name} ({trace.type})")
            else:
                print("❌ Price chart creation returned None")
        except Exception as e:
            print(f"❌ Error creating price chart: {e}")
        
        # Test RSI chart creation
        print(f"\nCreating multi-ticker RSI chart...")
        try:
            rsi_fig = create_multi_ticker_rsi_chart(successful_tickers, all_years_data)
            if rsi_fig:
                print(f"✅ RSI chart created successfully")
                print(f"  Chart title: {rsi_fig.layout.title.text}")
                print(f"  Number of traces: {len(rsi_fig.data)}")
                
                # Check traces (excluding reference lines)
                ticker_traces = [trace for trace in rsi_fig.data if 'RSI' in trace.name]
                print(f"  RSI traces: {len(ticker_traces)}")
                for trace in ticker_traces:
                    print(f"    {trace.name}")
            else:
                print("❌ RSI chart creation returned None")
        except Exception as e:
            print(f"❌ Error creating RSI chart: {e}")
    
    else:
        print("❌ No data available for chart testing")
    
    return len(successful_tickers) > 0

def test_data_quality():
    """Test data quality for multi-ticker analysis"""
    print("\nTesting Multi-Ticker Data Quality")
    print("=" * 50)
    
    # Test with different ticker combinations
    test_combinations = [
        ['AAPL', 'MSFT'],
        ['DEMO', 'TEST'],  # Demo data
        ['INVALID1', 'INVALID2']  # Invalid tickers
    ]
    
    for i, tickers in enumerate(test_combinations):
        print(f"\nTest {i+1}: {', '.join(tickers)}")
        
        success_count = 0
        for ticker in tickers:
            # Test single year of data
            start_date = datetime(2023, 1, 1)
            end_date = datetime(2023, 12, 31)
            
            data = fetch_stock_data(ticker, start_date, end_date)
            if data is not None and not data.empty:
                success_count += 1
                print(f"  ✅ {ticker}: {len(data)} days")
            else:
                print(f"  ❌ {ticker}: No data")
        
        success_rate = (success_count / len(tickers)) * 100
        print(f"  Success rate: {success_rate:.1f}%")

def main():
    """Run all multi-ticker tests"""
    print("Multi-Ticker Analysis Test Suite")
    print("=" * 60)
    
    # Run tests
    chart_success = test_multi_ticker_charts()
    test_data_quality()
    
    print("\n" + "=" * 60)
    if chart_success:
        print("✅ Multi-Ticker Tests Complete!")
    else:
        print("⚠️ Multi-Ticker Tests Completed with Issues")
    print("=" * 60)
    
    print("\nMulti-Ticker Features:")
    print("• Compare up to 10 stocks simultaneously")
    print("• Normalized price charts (all start at 100)")
    print("• Seasonal RSI pattern comparison")
    print("• Flexible date ranges and time periods")
    print("• Canadian stock support with .TO detection")
    print("• Performance statistics and data quality metrics")
    
    print("\nChart Features:")
    print("• Distinct colors for each ticker")
    print("• Interactive hover tooltips")
    print("• Normalized price comparison")
    print("• RSI overbought/oversold reference lines")
    print("• Month markers on seasonal charts")
    
    print("\nUsage Examples:")
    print("• Tech Giants: AAPL, MSFT, GOOGL, AMZN, META")
    print("• Canadian Banks: RY.TO, TD.TO, BMO.TO, BNS.TO")
    print("• Energy Sector: XOM, CVX, COP, EOG, SLB")
    print("• Mixed Portfolio: AAPL, SHOP.TO, TSLA, NVDA")

if __name__ == "__main__":
    main()
