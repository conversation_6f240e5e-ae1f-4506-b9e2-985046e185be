#!/usr/bin/env python3
"""
Test script for all the new features implemented
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import (
    fetch_stock_data, get_cache_summary, get_company_info_from_api,
    create_multi_year_line_chart, clear_cache
)
from datetime import datetime

def test_current_year_inclusion():
    """Test that current year is included in data"""
    print("Testing Current Year Inclusion")
    print("=" * 40)
    
    current_year = datetime.now().year
    print(f"Current year: {current_year}")
    
    # Test with demo data
    start_date = datetime(current_year, 1, 1)
    end_date = datetime(current_year, 12, 31)
    
    data = fetch_stock_data("DEMO", start_date, end_date)
    if data is not None:
        print(f"✓ Successfully fetched current year ({current_year}) data")
        print(f"  Date range: {data.index.min()} to {data.index.max()}")
        print(f"  Records: {len(data)}")
    else:
        print(f"✗ Failed to fetch current year data")

def test_company_info_api():
    """Test the new company info API"""
    print("\nTesting Company Info API")
    print("=" * 40)
    
    test_tickers = ['AAPL', 'MSFT', 'UNKNOWN_TICKER']
    
    for ticker in test_tickers:
        print(f"\nTesting {ticker}:")
        info = get_company_info_from_api(ticker)
        
        if info:
            print(f"  ✓ API returned data for {ticker}")
            print(f"    Name: {info['name']}")
            print(f"    Industry: {info['industry']}")
            print(f"    Market Cap: {info['market_cap']}")
            print(f"    Price Target: {info['analyst_predictions']['price_target']}")
        else:
            print(f"  ✗ No API data for {ticker} (will use fallback)")

def test_cache_summary():
    """Test the simplified cache summary"""
    print("\nTesting Cache Summary")
    print("=" * 40)
    
    # Clear cache first
    clear_cache()
    print("✓ Cache cleared for clean test")
    
    # Create some test cache entries
    test_scenarios = [
        ("DEMO", datetime(2023, 1, 1), datetime(2023, 6, 30)),  # Jan to Jun
        ("DEMO", datetime(2022, 1, 1), datetime(2022, 6, 30)),  # Same period, different year
        ("TEST", datetime(2023, 7, 1), datetime(2023, 12, 31)), # Jul to Dec
    ]
    
    print(f"\nCreating {len(test_scenarios)} cache entries...")
    for i, (ticker, start_date, end_date) in enumerate(test_scenarios):
        data = fetch_stock_data(ticker, start_date, end_date)
        if data is not None:
            print(f"  ✓ Cached {ticker} {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    
    # Test cache summary
    cache_summary = get_cache_summary()
    print(f"\nCache Summary Results:")
    print(f"Found {len(cache_summary)} unique combinations:")
    
    for item in cache_summary:
        print(f"  • {item['Ticker']}: Month {item['Start Month']} → {item['End Month']} ({item['Number of Years']} years)")
    
    # Clean up
    clear_cache()
    print("\n✓ Cache cleaned up")

def test_multi_year_line_chart():
    """Test the new multi-year line chart"""
    print("\nTesting Multi-Year Line Chart")
    print("=" * 40)
    
    # Create test data for multiple years
    years_data = {}
    test_years = [2021, 2022, 2023]
    
    for year in test_years:
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31)
        data = fetch_stock_data("DEMO", start_date, end_date)
        if data is not None:
            years_data[year] = data
            print(f"  ✓ Loaded {year} data: {len(data)} records")
    
    if years_data:
        fig = create_multi_year_line_chart("DEMO", years_data, 3)
        if fig:
            print(f"✓ Successfully created multi-year line chart")
            print(f"  Title: {fig.layout.title.text}")
            print(f"  Number of traces: {len(fig.data)}")
            print(f"  Chart type: Continuous line graph")
        else:
            print("✗ Failed to create multi-year line chart")
    else:
        print("✗ No data available for chart creation")

def test_all_features():
    """Test all new features together"""
    print("\n" + "=" * 60)
    print("COMPREHENSIVE FEATURE TEST")
    print("=" * 60)
    
    test_current_year_inclusion()
    test_company_info_api()
    test_cache_summary()
    test_multi_year_line_chart()
    
    print("\n" + "=" * 60)
    print("✅ ALL NEW FEATURES TESTED!")
    print("=" * 60)
    
    print("\nNew Features Summary:")
    print("1. ✅ Current year included in data fetching")
    print("2. ✅ Company info from yfinance API (with fallback)")
    print("3. ✅ Simplified cache table (ticker, start month, end month, years)")
    print("4. ✅ Clickable cache entries for quick reload")
    print("5. ✅ Multi-year continuous line chart")
    print("6. ✅ Average calculation by month (already implemented)")
    
    print("\nStreamlit App Features:")
    print("- Auto-refresh when settings change")
    print("- Clickable cache buttons in sidebar")
    print("- Continuous price history chart")
    print("- Real-time company information lookup")
    print("- Current year data inclusion")

if __name__ == "__main__":
    test_all_features()
