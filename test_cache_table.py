#!/usr/bin/env python3
"""
Test script to demonstrate the cache table functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import fetch_stock_data, get_cache_details, clear_cache
from datetime import datetime
import pandas as pd

def test_cache_table():
    """Test the cache table functionality"""
    print("Testing Cache Table Functionality")
    print("=" * 50)
    
    # Clear cache first
    clear_cache()
    print("✓ Cache cleared for clean test")
    
    # Create some test cache entries
    test_scenarios = [
        ("DEMO", datetime(2023, 1, 1), datetime(2023, 6, 30)),  # Jan to Jun
        ("DEMO", datetime(2023, 7, 1), datetime(2023, 12, 31)), # Jul to Dec
        ("TEST", datetime(2022, 11, 1), datetime(2023, 2, 28)), # Nov to Feb (cross-year)
        ("TEST", datetime(2021, 3, 1), datetime(2021, 8, 31)),  # Mar to Aug
    ]
    
    print(f"\nCreating {len(test_scenarios)} cache entries...")
    
    for i, (ticker, start_date, end_date) in enumerate(test_scenarios):
        print(f"  {i+1}. Fetching {ticker} from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        data = fetch_stock_data(ticker, start_date, end_date)
        if data is not None:
            print(f"     ✓ Cached {len(data)} records")
        else:
            print(f"     ✗ Failed to fetch data")
    
    # Test cache details
    print(f"\nTesting cache details extraction...")
    cache_details = get_cache_details()
    
    if cache_details:
        print(f"✓ Found {len(cache_details)} cache entries")
        
        # Create a summary table
        df = pd.DataFrame(cache_details)
        
        print("\nCache Table Contents:")
        print("=" * 80)
        print(df[['Ticker', 'Start Month', 'End Month', 'Start Date', 'End Date', 'Records', 'Size (KB)']].to_string(index=False))
        
        print("\nDetailed Cache Information:")
        print("-" * 50)
        for i, item in enumerate(cache_details):
            print(f"\nCache Entry {i+1}:")
            print(f"  Ticker: {item['Ticker']}")
            print(f"  Period: Month {item['Start Month']} → Month {item['End Month']}")
            print(f"  Date Range: {item['Start Date']} to {item['End Date']}")
            print(f"  Duration: {item['Days']} days")
            print(f"  Records: {item['Records']}")
            print(f"  File Size: {item['Size (KB)']} KB")
            print(f"  Age: {item['Age (hrs)']} hours")
            print(f"  Cached At: {item['Cached At']}")
        
        # Test sidebar display format
        print("\nSidebar Display Format:")
        print("-" * 30)
        for i, item in enumerate(cache_details[:3]):  # Show first 3
            ticker = item['Ticker']
            start_month = item['Start Month']
            end_month = item['End Month']
            records = item['Records']
            print(f"📁 {ticker}: {start_month}→{end_month} ({records} records)")
            print(f"   Period: Month {start_month} to {end_month}")
            print(f"   Records: {records}")
            print(f"   Size: {item['Size (KB)']} KB")
            print(f"   Age: {item['Age (hrs)']} hrs")
            print()
        
    else:
        print("✗ No cache details found")
    
    print("=" * 50)
    print("✅ Cache table functionality test completed!")
    
    print("\nCache Table Features:")
    print("- Shows ticker symbol for each cached item")
    print("- Displays start and end months for easy identification")
    print("- Shows exact date ranges and number of days")
    print("- Includes record count and file size")
    print("- Shows cache age and creation timestamp")
    print("- Compact sidebar display with expandable details")
    print("- Summary table for multiple cache entries")
    print("- Handles both new metadata format and legacy entries")
    
    # Clean up
    cleared = clear_cache()
    print(f"\n🧹 Cleaned up {cleared} cache files")

if __name__ == "__main__":
    test_cache_table()
