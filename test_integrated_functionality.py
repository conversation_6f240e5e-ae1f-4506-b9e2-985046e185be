#!/usr/bin/env python3
"""
Test script for Integrated Single/Multi-Ticker Functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_multi_ticker_price_chart, create_multi_ticker_rsi_chart, fetch_stock_data, normalize_to_day_of_year
from datetime import datetime

def test_ticker_parsing():
    """Test ticker input parsing logic"""
    print("Testing Ticker Input Parsing")
    print("=" * 40)
    
    test_cases = [
        ("AAPL", False, ["AAPL"]),
        ("AAPL, MSFT, GOOGL", True, ["AAPL", "MSFT", "GOOGL"]),
        ("AAPL,MSFT,GOOGL", True, ["AAPL", "MSFT", "GOOGL"]),
        ("AAPL, MSFT, GOOGL, TSLA, AMZN", True, ["AAPL", "MSFT", "GOOGL", "TSLA", "AMZN"]),
        ("  AAPL  ,  MSFT  ", True, ["AAPL", "MSFT"]),
        ("", False, ["AAPL"]),  # Default fallback
    ]
    
    for i, (ticker_input, expected_multi, expected_tickers) in enumerate(test_cases):
        print(f"\nTest {i+1}: '{ticker_input}'")
        
        # Simulate the parsing logic from the app
        if ',' in ticker_input:
            tickers = [t.strip() for t in ticker_input.split(',') if t.strip()]
            is_multi_ticker = True
            ticker = tickers[0] if tickers else "AAPL"
        else:
            tickers = [ticker_input.strip()] if ticker_input.strip() else ["AAPL"]
            is_multi_ticker = False
            ticker = tickers[0]
        
        print(f"  Expected Multi: {expected_multi}, Got: {is_multi_ticker}")
        print(f"  Expected Tickers: {expected_tickers}, Got: {tickers}")
        print(f"  Primary Ticker: {ticker}")
        
        if is_multi_ticker == expected_multi and tickers == expected_tickers:
            print("  ✅ PASS")
        else:
            print("  ❌ FAIL")
    
    print(f"\n✅ Ticker parsing tests complete!")

def test_integrated_multi_ticker_analysis():
    """Test the integrated multi-ticker analysis functionality"""
    print("\nTesting Integrated Multi-Ticker Analysis")
    print("=" * 50)
    
    # Test with a small set of tickers
    tickers = ['AAPL', 'MSFT']
    num_years = 2
    start_month = 1
    start_day = 1
    end_month = 12
    end_day = 31
    
    print(f"Testing with tickers: {', '.join(tickers)}")
    print(f"Analysis period: {num_years} years")
    
    # Simulate the multi-ticker data fetching logic
    all_years_data = {}
    failed_tickers = []
    current_year = datetime.now().year
    
    for current_ticker in tickers:
        print(f"\nFetching data for {current_ticker}...")
        ticker_years_data = {}
        
        for j in range(num_years):
            year = current_year - j
            
            try:
                start_date = datetime(year, start_month, start_day)
                end_date = datetime(year, end_month, end_day)
                
                # Handle year wrap-around
                if end_date < start_date:
                    end_date = datetime(year + 1, end_month, end_day)
                    
            except ValueError:
                # Handle invalid dates
                start_date = datetime(year, start_month, min(start_day, 28))
                end_date = datetime(year, end_month, min(end_day, 28))
                if end_date < start_date:
                    end_date = datetime(year + 1, end_month, min(end_day, 28))
            
            # Fetch data for this year
            data = fetch_stock_data(current_ticker, start_date, end_date)
            
            if data is not None and not data.empty:
                normalized_data = normalize_to_day_of_year(data, year)
                ticker_years_data[year] = normalized_data
                print(f"  ✅ {year}: {len(data)} days loaded")
            else:
                print(f"  ❌ {year}: No data")
        
        if ticker_years_data:
            all_years_data[current_ticker] = ticker_years_data
            print(f"  Total: {len(ticker_years_data)} years loaded for {current_ticker}")
        else:
            failed_tickers.append(current_ticker)
            print(f"  Failed to load any data for {current_ticker}")
    
    successful_tickers = list(all_years_data.keys())
    print(f"\nSuccessfully loaded data for: {', '.join(successful_tickers)}")
    
    if successful_tickers:
        # Test chart creation
        print(f"\nTesting multi-ticker chart creation...")
        
        try:
            # Test price chart
            price_fig = create_multi_ticker_price_chart(successful_tickers, all_years_data, num_years)
            if price_fig:
                print(f"✅ Multi-ticker price chart created successfully")
                print(f"  Chart title: {price_fig.layout.title.text}")
                print(f"  Number of traces: {len(price_fig.data)}")
            else:
                print("❌ Multi-ticker price chart creation failed")
            
            # Test RSI chart
            rsi_fig = create_multi_ticker_rsi_chart(successful_tickers, all_years_data)
            if rsi_fig:
                print(f"✅ Multi-ticker RSI chart created successfully")
                print(f"  Chart title: {rsi_fig.layout.title.text}")
                print(f"  Number of traces: {len(rsi_fig.data)}")
            else:
                print("❌ Multi-ticker RSI chart creation failed")
                
        except Exception as e:
            print(f"❌ Chart creation error: {e}")
    
    else:
        print("❌ No data available for chart testing")
    
    return len(successful_tickers) > 0

def test_ticker_limit():
    """Test the 10-ticker limit functionality"""
    print("\nTesting Ticker Limit (10 max)")
    print("=" * 40)
    
    # Test with more than 10 tickers
    many_tickers = "AAPL, MSFT, GOOGL, AMZN, TSLA, META, NVDA, AMD, INTC, CRM, ORCL, ADBE"
    tickers = [t.strip() for t in many_tickers.split(',') if t.strip()]
    
    print(f"Input tickers ({len(tickers)}): {', '.join(tickers)}")
    
    # Apply limit
    if len(tickers) > 10:
        print("⚠️ Maximum 10 tickers allowed for performance reasons")
        tickers = tickers[:10]
    
    print(f"Limited tickers ({len(tickers)}): {', '.join(tickers)}")
    
    if len(tickers) == 10:
        print("✅ Ticker limit working correctly")
    else:
        print("❌ Ticker limit not working correctly")

def main():
    """Run all integrated functionality tests"""
    print("Integrated Single/Multi-Ticker Analysis Test Suite")
    print("=" * 60)
    
    # Run tests
    test_ticker_parsing()
    chart_success = test_integrated_multi_ticker_analysis()
    test_ticker_limit()
    
    print("\n" + "=" * 60)
    if chart_success:
        print("✅ Integrated Functionality Tests Complete!")
    else:
        print("⚠️ Integrated Functionality Tests Completed with Issues")
    print("=" * 60)
    
    print("\nIntegrated Application Features:")
    print("• Single page with both single and multi-ticker analysis")
    print("• Automatic detection of single vs multiple ticker input")
    print("• Same seasonal date range and time period controls")
    print("• Cache functionality for single stock analyses")
    print("• Multi-ticker comparison with up to 10 stocks")
    print("• Consistent loading experience for all analysis types")
    
    print("\nInput Examples:")
    print("• Single Stock: 'AAPL' → Detailed seasonal analysis")
    print("• Multi-Ticker: 'AAPL, MSFT, GOOGL' → Comparison charts")
    print("• Canadian Stocks: 'RY.TO, TD.TO' → Auto .TO detection")
    print("• Mixed: 'AAPL, SHOP.TO, TSLA' → US + Canadian comparison")
    
    print("\nChart Types:")
    print("• Single Stock: 5 detailed charts + company info + statistics")
    print("• Multi-Ticker: 2 comparison charts + summary table")
    print("• Cache Loading: Same experience as fresh analysis")

if __name__ == "__main__":
    main()
