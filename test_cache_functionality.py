#!/usr/bin/env python3
"""
Test script for Cache Functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import save_to_cache, load_from_cache, get_all_cached_entries, clear_cache
from datetime import datetime

def test_cache_operations():
    """Test basic cache operations"""
    print("Testing Cache Operations")
    print("=" * 40)
    
    # Clear cache first
    cleared = clear_cache()
    print(f"Cleared {cleared} existing cache entries")
    
    # Test data
    test_ticker = "AAPL"
    test_years = 3
    test_start_month = 1
    test_start_day = 1
    test_end_month = 12
    test_end_day = 31
    
    # Create test data structure
    test_years_data = {
        2023: {
            'data': 'mock_data_2023',
            'year': 2023
        },
        2022: {
            'data': 'mock_data_2022', 
            'year': 2022
        },
        2021: {
            'data': 'mock_data_2021',
            'year': 2021
        }
    }
    
    print(f"\nTesting cache save for {test_ticker}...")
    
    # Save to cache
    try:
        save_to_cache(
            test_ticker, 
            test_years, 
            test_start_month, 
            test_start_day,
            test_end_month, 
            test_end_day, 
            test_years_data
        )
        print("✅ Cache save successful")
    except Exception as e:
        print(f"❌ Cache save failed: {e}")
        return False
    
    # Test cache retrieval
    print(f"\nTesting cache load for {test_ticker}...")
    try:
        loaded_data = load_from_cache(
            test_ticker,
            test_years,
            test_start_month,
            test_start_day,
            test_end_month,
            test_end_day
        )
        
        if loaded_data:
            print("✅ Cache load successful")
            print(f"  Loaded years: {list(loaded_data['years_data'].keys())}")
            print(f"  Cache timestamp: {loaded_data.get('timestamp', 'N/A')}")
        else:
            print("❌ Cache load returned None")
            return False
    except Exception as e:
        print(f"❌ Cache load failed: {e}")
        return False
    
    # Test cache listing
    print(f"\nTesting cache listing...")
    try:
        cached_entries = get_all_cached_entries()
        print(f"✅ Found {len(cached_entries)} cached entries")
        
        if cached_entries:
            entry = cached_entries[0]
            print(f"  First entry: {entry['ticker']} ({entry['num_years']}y)")
            print(f"  Date range: {entry['start_month']}/{entry['start_day']} - {entry['end_month']}/{entry['end_day']}")
        
    except Exception as e:
        print(f"❌ Cache listing failed: {e}")
        return False
    
    return True

def test_cache_button_data():
    """Test the data structure expected by cache buttons"""
    print("\nTesting Cache Button Data Structure")
    print("=" * 50)
    
    # Get cached entries
    cached_entries = get_all_cached_entries()
    
    if not cached_entries:
        print("❌ No cached entries found for testing")
        return False
    
    print(f"Testing with {len(cached_entries)} cached entries")
    
    for i, entry in enumerate(cached_entries[:3]):  # Test first 3 entries
        print(f"\nEntry {i+1}:")
        print(f"  Ticker: {entry.get('ticker', 'MISSING')}")
        print(f"  Years: {entry.get('num_years', 'MISSING')}")
        print(f"  Start: {entry.get('start_month', 'MISSING')}/{entry.get('start_day', 'MISSING')}")
        print(f"  End: {entry.get('end_month', 'MISSING')}/{entry.get('end_day', 'MISSING')}")
        print(f"  Cache Key: {entry.get('cache_key', 'MISSING')[:16]}...")
        
        # Test button text generation
        try:
            date_range = f"{entry['start_month']}/{entry['start_day']} - {entry['end_month']}/{entry['end_day']}"
            button_text = f"{entry['ticker']} ({entry['num_years']}y) {date_range}"
            button_key = f"cache_load_{i}_{entry['cache_key'][:8]}"
            
            print(f"  Button Text: {button_text}")
            print(f"  Button Key: {button_key}")
            print("  ✅ Button data structure valid")
            
        except KeyError as e:
            print(f"  ❌ Missing required field: {e}")
            return False
        except Exception as e:
            print(f"  ❌ Button data error: {e}")
            return False
    
    return True

def test_cache_load_simulation():
    """Simulate the cache load process"""
    print("\nTesting Cache Load Simulation")
    print("=" * 50)
    
    # Get first cached entry
    cached_entries = get_all_cached_entries()
    
    if not cached_entries:
        print("❌ No cached entries available for simulation")
        return False
    
    entry = cached_entries[0]
    print(f"Simulating load of: {entry['ticker']} ({entry['num_years']}y)")
    
    # Simulate the cache load process
    try:
        cached_data = load_from_cache(
            entry['ticker'], 
            entry['num_years'],
            entry['start_month'],
            entry['start_day'], 
            entry['end_month'],
            entry['end_day']
        )
        
        if cached_data:
            print("✅ Cache data loaded successfully")
            
            # Simulate session state updates
            session_state_updates = {
                'load_cached_data': cached_data,
                'cached_ticker': entry['ticker'],
                'cached_num_years': entry['num_years'],
                'cached_start_month': entry['start_month'],
                'cached_start_day': entry['start_day'],
                'cached_end_month': entry['end_month'],
                'cached_end_day': entry['end_day'],
                'auto_analyze_cache': True
            }
            
            print("✅ Session state updates prepared:")
            for key, value in session_state_updates.items():
                if key == 'load_cached_data':
                    print(f"  {key}: <cached_data_object>")
                else:
                    print(f"  {key}: {value}")
            
            # Verify data structure
            years_data = cached_data.get('years_data', {})
            print(f"✅ Years data contains: {list(years_data.keys())}")
            
            return True
        else:
            print("❌ Cache load returned None")
            return False
            
    except Exception as e:
        print(f"❌ Cache load simulation failed: {e}")
        return False

def main():
    """Run all cache functionality tests"""
    print("Cache Functionality Test Suite")
    print("=" * 60)
    
    # Run tests
    basic_ops = test_cache_operations()
    button_data = test_cache_button_data()
    load_sim = test_cache_load_simulation()
    
    print("\n" + "=" * 60)
    if basic_ops and button_data and load_sim:
        print("✅ All Cache Tests Passed!")
    else:
        print("⚠️ Some Cache Tests Failed")
    print("=" * 60)
    
    print("\nCache Button Enhancement:")
    print("• Clicking cached analysis buttons now auto-loads graphics")
    print("• Form controls automatically update to cached values")
    print("• No need to click 'Analyze' button after loading from cache")
    print("• Immediate display of charts and analysis")
    
    print("\nImplementation Details:")
    print("• auto_analyze_cache flag triggers automatic analysis")
    print("• Form controls use cached values as defaults")
    print("• Session state properly manages cache-to-display flow")
    print("• Cached data includes complete years_data structure")
    
    print("\nUser Experience:")
    print("• Click cache button → Graphics appear immediately")
    print("• Form controls show cached parameters")
    print("• Same behavior as manual ticker entry + analyze")
    print("• Seamless transition from cache to live analysis")

if __name__ == "__main__":
    main()
