#!/usr/bin/env python3
"""
Test script for the MarketWatch link feature
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import get_company_info

def test_marketwatch_link():
    """Test the MarketWatch link generation"""
    print("Testing MarketWatch Link Feature")
    print("=" * 50)
    
    test_tickers = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'AMZN']
    
    for ticker in test_tickers:
        print(f"\nTesting {ticker}:")
        
        # Get company info
        company_info = get_company_info(ticker)
        company_name = company_info['name']
        
        # Generate MarketWatch URL
        marketwatch_url = f"https://www.marketwatch.com/investing/stock/{ticker.lower()}"
        
        # Show how it will appear in Streamlit
        markdown_link = f"🏢 [{company_name}]({marketwatch_url})"
        
        print(f"  Company: {company_name}")
        print(f"  URL: {marketwatch_url}")
        print(f"  Markdown: {markdown_link}")
        print(f"  ✓ Link generated successfully")
    
    print("\n" + "=" * 50)
    print("✅ MarketWatch Link Feature Working!")
    print("=" * 50)
    
    print("\nFeature Details:")
    print("- Company name becomes clickable link")
    print("- Links to MarketWatch stock page")
    print("- URL format: https://www.marketwatch.com/investing/stock/{ticker}")
    print("- Ticker automatically converted to lowercase")
    print("- Opens in new tab when clicked")
    
    print("\nExample URLs:")
    for ticker in ['AAPL', 'MSFT', 'GOOGL']:
        url = f"https://www.marketwatch.com/investing/stock/{ticker.lower()}"
        print(f"  {ticker}: {url}")

if __name__ == "__main__":
    test_marketwatch_link()
