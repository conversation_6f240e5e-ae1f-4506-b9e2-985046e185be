#!/usr/bin/env python3
"""
Test script for Ticker Input Persistence
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ticker_persistence_logic():
    """Test the logic for ticker input persistence"""
    print("Testing Ticker Input Persistence Logic")
    print("=" * 50)
    
    # Test scenarios
    test_scenarios = [
        {
            'name': 'Fresh session (no history)',
            'session_state': {},
            'expected_default': '',
            'description': 'Should start with empty input, no AAPL default'
        },
        {
            'name': 'User entered MSFT previously',
            'session_state': {'last_used_ticker': 'MSFT'},
            'expected_default': 'MSFT',
            'description': 'Should remember last manually entered ticker'
        },
        {
            'name': 'User entered multi-ticker previously',
            'session_state': {'last_used_ticker': 'AAPL, MSFT, GOOGL'},
            'expected_default': 'AAPL, MSFT, GOOGL',
            'description': 'Should remember last multi-ticker input'
        },
        {
            'name': 'Single ticker cache loaded',
            'session_state': {'cached_ticker': 'TSLA'},
            'expected_default': 'TSLA',
            'description': 'Should use cached single ticker and remember it'
        },
        {
            'name': 'Multi-ticker cache loaded',
            'session_state': {'cached_tickers': ['NVDA', 'AMD', 'INTC']},
            'expected_default': 'NVDA, AMD, INTC',
            'description': 'Should use cached multi-tickers and remember them'
        },
        {
            'name': 'Cache overrides previous manual entry',
            'session_state': {'last_used_ticker': 'AAPL', 'cached_ticker': 'GOOGL'},
            'expected_default': 'GOOGL',
            'description': 'Cache should take priority over manual history'
        },
        {
            'name': 'Multi-cache overrides single cache',
            'session_state': {'cached_ticker': 'AAPL', 'cached_tickers': ['MSFT', 'GOOGL']},
            'expected_default': 'MSFT, GOOGL',
            'description': 'Multi-ticker cache should take priority'
        }
    ]
    
    for i, scenario in enumerate(test_scenarios):
        print(f"\nTest {i+1}: {scenario['name']}")
        print(f"  Description: {scenario['description']}")
        
        # Simulate the logic from the app
        session_state = scenario['session_state'].copy()
        
        # Handle ticker input persistence - remember last used ticker(s)
        if 'reload_from_cache' in session_state and session_state['reload_from_cache']:
            default_ticker = session_state.get('selected_ticker', '')
            session_state['reload_from_cache'] = False
        elif 'cached_ticker' in session_state:
            # Single ticker from cache
            default_ticker = session_state['cached_ticker']
            # Remember this as the last used ticker
            session_state['last_used_ticker'] = session_state['cached_ticker']
        elif 'cached_tickers' in session_state:
            # Multi-ticker cache - join with commas
            default_ticker = ', '.join(session_state['cached_tickers'])
            # Remember this as the last used ticker(s)
            session_state['last_used_ticker'] = ', '.join(session_state['cached_tickers'])
        else:
            # Use last used ticker if available, otherwise empty
            default_ticker = session_state.get('last_used_ticker', '')
        
        print(f"  Session state: {scenario['session_state']}")
        print(f"  Expected default: '{scenario['expected_default']}'")
        print(f"  Actual default: '{default_ticker}'")
        
        if default_ticker == scenario['expected_default']:
            print(f"  ✅ PASS")
        else:
            print(f"  ❌ FAIL")
    
    print(f"\n✅ Ticker persistence logic tests complete!")

def test_ticker_input_processing():
    """Test ticker input processing with persistence"""
    print("\nTesting Ticker Input Processing")
    print("=" * 50)
    
    # Test input scenarios
    input_scenarios = [
        {
            'input': '',
            'expected_tickers': [],
            'expected_multi': False,
            'expected_ticker': '',
            'should_remember': False,
            'description': 'Empty input should not be processed'
        },
        {
            'input': 'AAPL',
            'expected_tickers': ['AAPL'],
            'expected_multi': False,
            'expected_ticker': 'AAPL',
            'should_remember': True,
            'description': 'Single ticker should be processed and remembered'
        },
        {
            'input': 'AAPL, MSFT, GOOGL',
            'expected_tickers': ['AAPL', 'MSFT', 'GOOGL'],
            'expected_multi': True,
            'expected_ticker': 'AAPL',
            'should_remember': True,
            'description': 'Multi-ticker should be processed and remembered'
        },
        {
            'input': '  TSLA  ,  NVDA  ',
            'expected_tickers': ['TSLA', 'NVDA'],
            'expected_multi': True,
            'expected_ticker': 'TSLA',
            'should_remember': True,
            'description': 'Whitespace should be cleaned up'
        },
        {
            'input': 'SHOP.TO',
            'expected_tickers': ['SHOP.TO'],
            'expected_multi': False,
            'expected_ticker': 'SHOP.TO',
            'should_remember': True,
            'description': 'Canadian ticker should be processed'
        }
    ]
    
    for i, scenario in enumerate(input_scenarios):
        print(f"\nTest {i+1}: {scenario['description']}")
        
        # Simulate the parsing logic
        session_state = {}
        ticker_input = scenario['input']
        
        # Parse ticker input - detect single vs multiple tickers
        if ticker_input.strip():  # Only process if there's actual input
            # Remember this as the last used ticker for future sessions
            session_state['last_used_ticker'] = ticker_input
            
            if ',' in ticker_input:
                # Multiple tickers - split and clean
                tickers = [t.strip() for t in ticker_input.split(',') if t.strip()]
                is_multi_ticker = True
                ticker = tickers[0] if tickers else ""
            else:
                # Single ticker
                tickers = [ticker_input.strip()]
                is_multi_ticker = False
                ticker = tickers[0]
        else:
            # Empty input - no analysis possible
            tickers = []
            is_multi_ticker = False
            ticker = ""
        
        print(f"  Input: '{scenario['input']}'")
        print(f"  Expected tickers: {scenario['expected_tickers']}")
        print(f"  Actual tickers: {tickers}")
        print(f"  Expected multi: {scenario['expected_multi']}")
        print(f"  Actual multi: {is_multi_ticker}")
        print(f"  Expected primary ticker: '{scenario['expected_ticker']}'")
        print(f"  Actual primary ticker: '{ticker}'")
        
        # Check if should remember
        remembered = 'last_used_ticker' in session_state
        print(f"  Should remember: {scenario['should_remember']}")
        print(f"  Actually remembered: {remembered}")
        if remembered:
            print(f"  Remembered value: '{session_state['last_used_ticker']}'")
        
        # Validate results
        tests_passed = (
            tickers == scenario['expected_tickers'] and
            is_multi_ticker == scenario['expected_multi'] and
            ticker == scenario['expected_ticker'] and
            remembered == scenario['should_remember']
        )
        
        if tests_passed:
            print(f"  ✅ PASS")
        else:
            print(f"  ❌ FAIL")
    
    print(f"\n✅ Ticker input processing tests complete!")

def test_user_experience_flow():
    """Test the complete user experience flow"""
    print("\nTesting User Experience Flow")
    print("=" * 50)
    
    print("User Journey Simulation:")
    print("1. Fresh app load:")
    print("   → Ticker input is empty (no AAPL default)")
    print("   → Placeholder shows: 'Enter ticker(s) e.g., AAPL or AAPL, MSFT, GOOGL'")
    print("   → User must enter ticker to proceed")
    print()
    
    print("2. User enters 'MSFT' and analyzes:")
    print("   → st.session_state.last_used_ticker = 'MSFT'")
    print("   → Analysis proceeds with MSFT")
    print("   → Ticker input shows 'MSFT'")
    print()
    
    print("3. User clicks quick year button (e.g., 10 Years):")
    print("   → Page reloads")
    print("   → Ticker input still shows 'MSFT' (remembered)")
    print("   → Years slider shows 10")
    print()
    
    print("4. User clicks cached analysis button:")
    print("   → Cached ticker loads (e.g., 'GOOGL')")
    print("   → st.session_state.last_used_ticker = 'GOOGL'")
    print("   → Ticker input shows 'GOOGL'")
    print("   → Analysis displays immediately")
    print()
    
    print("5. User modifies date range:")
    print("   → Page reloads")
    print("   → Ticker input still shows 'GOOGL' (remembered from cache)")
    print("   → Date controls show new values")
    print()
    
    print("6. User enters multi-ticker 'AAPL, MSFT, TSLA':")
    print("   → st.session_state.last_used_ticker = 'AAPL, MSFT, TSLA'")
    print("   → Multi-ticker analysis proceeds")
    print("   → Ticker input shows 'AAPL, MSFT, TSLA'")
    print()
    
    print("7. User clicks cached multi-ticker analysis:")
    print("   → Cached multi-tickers load (e.g., 'NVDA, AMD')")
    print("   → st.session_state.last_used_ticker = 'NVDA, AMD'")
    print("   → Ticker input shows 'NVDA, AMD'")
    print("   → Multi-ticker analysis displays immediately")
    print()
    
    print("Benefits:")
    benefits = [
        "No unwanted AAPL defaults cluttering the interface",
        "User's last choice is always remembered",
        "Cache selections update the remembered ticker",
        "Seamless transition between single and multi-ticker",
        "Consistent behavior across all app interactions"
    ]
    
    for benefit in benefits:
        print(f"  ✅ {benefit}")
    
    print("\n✅ User experience flow validation complete!")

def main():
    """Run all ticker persistence tests"""
    print("Ticker Input Persistence Test Suite")
    print("=" * 60)
    
    # Run tests
    test_ticker_persistence_logic()
    test_ticker_input_processing()
    test_user_experience_flow()
    
    print("\n" + "=" * 60)
    print("✅ All Ticker Persistence Tests Complete!")
    print("=" * 60)
    
    print("\nTicker Persistence Features:")
    print("• No AAPL default - starts with empty input")
    print("• Remembers last manually entered ticker(s)")
    print("• Cache selections update remembered ticker")
    print("• Supports both single and multi-ticker persistence")
    print("• Consistent behavior across all app interactions")
    
    print("\nImplementation Benefits:")
    print("• Cleaner user interface without unwanted defaults")
    print("• Improved workflow continuity")
    print("• Better user experience with remembered choices")
    print("• Seamless integration with cache functionality")
    print("• Professional application behavior")

if __name__ == "__main__":
    main()
