#!/usr/bin/env python3
"""
Test script for the caching functionality
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import (
    fetch_stock_data, get_cached_data, save_to_cache, 
    get_cache_info, clear_cache, ensure_cache_dir
)
from datetime import datetime

def test_cache_functionality():
    """Test the caching system"""
    print("Testing Stock Data Caching System")
    print("=" * 50)
    
    # Test cache directory creation
    print("\n1. Testing cache directory creation...")
    ensure_cache_dir()
    print("✓ Cache directory created/verified")
    
    # Test cache info (should be empty initially)
    print("\n2. Testing cache info...")
    files, size = get_cache_info()
    print(f"✓ Cache files: {files}, Size: {size} bytes")
    
    # Test data fetching and caching
    print("\n3. Testing data fetching and caching...")
    ticker = "DEMO"  # Use demo data for consistent testing
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 12, 31)
    
    print(f"   Fetching {ticker} data for first time...")
    start_time = time.time()
    data1 = fetch_stock_data(ticker, start_date, end_date)
    first_fetch_time = time.time() - start_time
    
    if data1 is not None:
        print(f"✓ First fetch successful: {len(data1)} rows in {first_fetch_time:.2f}s")
    else:
        print("✗ First fetch failed")
        return
    
    # Test cache retrieval
    print(f"   Fetching {ticker} data for second time (should be cached)...")
    start_time = time.time()
    data2 = fetch_stock_data(ticker, start_date, end_date)
    second_fetch_time = time.time() - start_time
    
    if data2 is not None:
        print(f"✓ Second fetch successful: {len(data2)} rows in {second_fetch_time:.2f}s")
        
        # Check if second fetch was faster (indicating cache hit)
        if second_fetch_time < first_fetch_time:
            print(f"✓ Cache working! Second fetch {first_fetch_time/second_fetch_time:.1f}x faster")
        else:
            print("⚠ Cache may not be working (second fetch not faster)")
    else:
        print("✗ Second fetch failed")
    
    # Test cache info after caching
    print("\n4. Testing cache info after caching...")
    files, size = get_cache_info()
    size_kb = size / 1024 if size > 0 else 0
    print(f"✓ Cache files: {files}, Size: {size_kb:.1f} KB")
    
    # Test direct cache retrieval
    print("\n5. Testing direct cache retrieval...")
    cached_data = get_cached_data(ticker, start_date, end_date)
    if cached_data is not None:
        print(f"✓ Direct cache retrieval successful: {len(cached_data)} rows")
    else:
        print("✗ Direct cache retrieval failed")
    
    # Test cache clearing
    print("\n6. Testing cache clearing...")
    cleared_files = clear_cache()
    print(f"✓ Cleared {cleared_files} cache files")
    
    # Verify cache is empty
    files, size = get_cache_info()
    if files == 0 and size == 0:
        print("✓ Cache successfully cleared")
    else:
        print("⚠ Cache may not be completely cleared")
    
    print("\n" + "=" * 50)
    print("✅ Cache functionality test completed!")
    
    print("\nCache Features:")
    print("- Automatic caching of fetched stock data")
    print("- 24-hour cache expiry for data freshness")
    print("- File-based storage in 'stock_data_cache' directory")
    print("- Cache management controls in Streamlit sidebar")
    print("- Significant speed improvement for repeated requests")

def test_multiple_tickers():
    """Test caching with multiple tickers"""
    print("\n" + "=" * 50)
    print("Testing Multiple Ticker Caching")
    print("=" * 50)
    
    tickers = ["DEMO", "TEST"]
    start_date = datetime(2022, 1, 1)
    end_date = datetime(2022, 12, 31)
    
    for ticker in tickers:
        print(f"\nTesting {ticker}...")
        data = fetch_stock_data(ticker, start_date, end_date)
        if data is not None:
            print(f"✓ {ticker} data cached: {len(data)} rows")
        else:
            print(f"✗ {ticker} data failed")
    
    # Check cache info
    files, size = get_cache_info()
    print(f"\n✓ Total cached files: {files}")
    print(f"✓ Total cache size: {size/1024:.1f} KB")
    
    # Clean up
    clear_cache()
    print("✓ Cache cleaned up")

if __name__ == "__main__":
    test_cache_functionality()
    test_multiple_tickers()
