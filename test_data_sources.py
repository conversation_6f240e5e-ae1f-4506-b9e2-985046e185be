#!/usr/bin/env python3
"""
Test script to check various stock data sources and find the most reliable one
"""

import sys
import os
from datetime import datetime, timedelta

def test_yfinance():
    """Test Yahoo Finance via yfinance library"""
    print("Testing Yahoo Finance (yfinance)...")
    try:
        import yfinance as yf
        
        # Test with a simple request
        ticker = yf.Ticker("AAPL")
        data = ticker.history(period="5d")
        
        if not data.empty:
            print(f"✓ Yahoo Finance working - got {len(data)} days of data")
            print(f"  Latest close: ${data['Close'].iloc[-1]:.2f}")
            return True, "yfinance"
        else:
            print("✗ Yahoo Finance returned empty data")
            return False, None
            
    except Exception as e:
        print(f"✗ Yahoo Finance failed: {e}")
        return False, None

def test_alpha_vantage():
    """Test Alpha Vantage (requires API key)"""
    print("\nTesting Alpha Vantage...")
    try:
        import requests
        
        # This would require an API key, but let's test the endpoint
        url = "https://www.alphavantage.co/query"
        params = {
            "function": "TIME_SERIES_DAILY",
            "symbol": "AAPL",
            "apikey": "demo"  # Demo key - limited functionality
        }
        
        response = requests.get(url, params=params, timeout=10)
        data = response.json()
        
        if "Time Series (Daily)" in data:
            print("✓ Alpha Vantage working (requires API key for full access)")
            return True, "alpha_vantage"
        else:
            print("✗ Alpha Vantage requires API key")
            return False, None
            
    except Exception as e:
        print(f"✗ Alpha Vantage failed: {e}")
        return False, None

def test_finnhub():
    """Test Finnhub (free tier available)"""
    print("\nTesting Finnhub...")
    try:
        import requests
        
        # Test with free tier (limited requests)
        url = "https://finnhub.io/api/v1/quote"
        params = {
            "symbol": "AAPL",
            "token": "demo"  # Demo token
        }
        
        response = requests.get(url, params=params, timeout=10)
        data = response.json()
        
        if "c" in data and data["c"] > 0:  # 'c' is current price
            print(f"✓ Finnhub working - AAPL price: ${data['c']}")
            return True, "finnhub"
        else:
            print("✗ Finnhub requires API key")
            return False, None
            
    except Exception as e:
        print(f"✗ Finnhub failed: {e}")
        return False, None

def test_yahoo_direct():
    """Test Yahoo Finance directly via requests"""
    print("\nTesting Yahoo Finance (direct API)...")
    try:
        import requests
        import json
        
        # Direct Yahoo Finance API call
        url = "https://query1.finance.yahoo.com/v8/finance/chart/AAPL"
        params = {
            "range": "5d",
            "interval": "1d"
        }
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, params=params, headers=headers, timeout=10)
        data = response.json()
        
        if "chart" in data and data["chart"]["result"]:
            prices = data["chart"]["result"][0]["indicators"]["quote"][0]["close"]
            latest_price = [p for p in prices if p is not None][-1]
            print(f"✓ Yahoo Direct API working - AAPL price: ${latest_price:.2f}")
            return True, "yahoo_direct"
        else:
            print("✗ Yahoo Direct API failed")
            return False, None
            
    except Exception as e:
        print(f"✗ Yahoo Direct API failed: {e}")
        return False, None

def test_polygon():
    """Test Polygon.io (free tier available)"""
    print("\nTesting Polygon.io...")
    try:
        import requests
        
        # Test with free tier
        url = "https://api.polygon.io/v2/aggs/ticker/AAPL/prev"
        params = {
            "apikey": "demo"  # Demo key
        }
        
        response = requests.get(url, params=params, timeout=10)
        data = response.json()
        
        if "results" in data and data["results"]:
            price = data["results"][0]["c"]  # Close price
            print(f"✓ Polygon.io working - AAPL price: ${price}")
            return True, "polygon"
        else:
            print("✗ Polygon.io requires API key")
            return False, None
            
    except Exception as e:
        print(f"✗ Polygon.io failed: {e}")
        return False, None

def test_demo_data():
    """Test demo data generation as fallback"""
    print("\nTesting demo data generation...")
    try:
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        
        # Generate sample data
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        
        np.random.seed(42)
        prices = 150 + np.cumsum(np.random.normal(0, 2, len(dates)))
        
        data = pd.DataFrame({
            'Close': prices,
            'Open': prices * 0.99,
            'High': prices * 1.02,
            'Low': prices * 0.98,
            'Volume': np.random.randint(1000000, 10000000, len(dates))
        }, index=dates)
        
        print(f"✓ Demo data generation working - {len(data)} days generated")
        print(f"  Sample price range: ${data['Close'].min():.2f} - ${data['Close'].max():.2f}")
        return True, "demo_data"
        
    except Exception as e:
        print(f"✗ Demo data generation failed: {e}")
        return False, None

def main():
    """Test all data sources and recommend the best option"""
    print("Testing Stock Data Sources")
    print("=" * 50)
    
    # Test all sources
    sources = [
        test_yfinance,
        test_yahoo_direct,
        test_alpha_vantage,
        test_finnhub,
        test_polygon,
        test_demo_data
    ]
    
    working_sources = []
    
    for test_func in sources:
        try:
            success, source_name = test_func()
            if success:
                working_sources.append(source_name)
        except Exception as e:
            print(f"Error testing {test_func.__name__}: {e}")
    
    print("\n" + "=" * 50)
    print("RESULTS:")
    
    if working_sources:
        print(f"✅ Working sources: {', '.join(working_sources)}")
        
        # Recommend best option
        if "yfinance" in working_sources:
            print("\n🎯 RECOMMENDATION: Use yfinance (easiest, no API key needed)")
        elif "yahoo_direct" in working_sources:
            print("\n🎯 RECOMMENDATION: Use Yahoo Direct API (no API key needed)")
        elif any(src in working_sources for src in ["alpha_vantage", "finnhub", "polygon"]):
            print("\n🎯 RECOMMENDATION: Use API service (requires free API key)")
        else:
            print("\n🎯 RECOMMENDATION: Use demo data for testing")
            
    else:
        print("❌ No working sources found")
        
    print("\n📝 NOTES:")
    print("- Yahoo Finance (yfinance): Free, no API key, but can be unreliable")
    print("- Alpha Vantage: Free tier (5 calls/min), requires API key")
    print("- Finnhub: Free tier (60 calls/min), requires API key") 
    print("- Polygon.io: Free tier (5 calls/min), requires API key")
    print("- Demo data: Always works, good for testing")

if __name__ == "__main__":
    main()
