import streamlit as st
import yfinance as yf
import pandas as pd
import plotly.graph_objects as go
from datetime import datetime, timedelta
import numpy as np
import random
import os
import pickle
import hashlib
import requests
from bs4 import BeautifulSoup
import time



def get_cache_key(ticker, num_years):
    """Generate a unique cache key for the given parameters"""
    key_string = f"{ticker}_{num_years}_1_1_12_31"  # Always Jan 1 to Dec 31
    return hashlib.md5(key_string.encode()).hexdigest()

def get_multi_ticker_cache_key(tickers, num_years):
    """Generate a unique cache key for multi-ticker analysis"""
    # Sort tickers to ensure consistent key regardless of input order
    sorted_tickers = sorted(tickers)
    tickers_string = ",".join(sorted_tickers)
    key_string = f"MULTI_{tickers_string}_{num_years}_1_1_12_31"  # Always Jan 1 to Dec 31
    return hashlib.md5(key_string.encode()).hexdigest()

def get_cache_dir():
    """Get or create cache directory"""
    cache_dir = os.path.join(os.getcwd(), "stock_cache")
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir)
    return cache_dir

def save_to_cache(ticker, num_years, years_data, company_info=None):
    """Save years_data to cache with company information"""
    try:
        cache_key = get_cache_key(ticker, num_years)
        cache_dir = get_cache_dir()
        cache_file = os.path.join(cache_dir, f"{cache_key}.pkl")

        # Get company info if not provided
        if company_info is None:
            company_info = get_company_info_online(ticker)

        cache_data = {
            'type': 'single',
            'ticker': ticker,
            'num_years': num_years,
            'start_month': 1,  # Always January
            'start_day': 1,    # Always 1st
            'end_month': 12,   # Always December
            'end_day': 31,     # Always 31st
            'years_data': years_data,
            'company_info': company_info,
            'timestamp': datetime.now()
        }

        with open(cache_file, 'wb') as f:
            pickle.dump(cache_data, f)

        return True
    except Exception as e:
        return False

def save_multi_ticker_to_cache(tickers, num_years, all_years_data):
    """Save multi-ticker analysis data to cache"""
    try:
        cache_key = get_multi_ticker_cache_key(tickers, num_years)
        cache_dir = get_cache_dir()
        cache_file = os.path.join(cache_dir, f"{cache_key}.pkl")

        cache_data = {
            'type': 'multi',
            'tickers': sorted(tickers),  # Store sorted for consistency
            'num_years': num_years,
            'start_month': 1,  # Always January
            'start_day': 1,    # Always 1st
            'end_month': 12,   # Always December
            'end_day': 31,     # Always 31st
            'all_years_data': all_years_data,
            'timestamp': datetime.now()
        }

        with open(cache_file, 'wb') as f:
            pickle.dump(cache_data, f)

        return True
    except Exception as e:
        return False

def load_from_cache(ticker, num_years):
    """Load years_data from cache"""
    try:
        cache_key = get_cache_key(ticker, num_years)
        cache_dir = get_cache_dir()
        cache_file = os.path.join(cache_dir, f"{cache_key}.pkl")

        if os.path.exists(cache_file):
            with open(cache_file, 'rb') as f:
                cache_data = pickle.load(f)
            return cache_data

        return None
    except Exception as e:
        return None

def load_multi_ticker_from_cache(tickers, num_years):
    """Load multi-ticker analysis data from cache"""
    try:
        cache_key = get_multi_ticker_cache_key(tickers, num_years)
        cache_dir = get_cache_dir()
        cache_file = os.path.join(cache_dir, f"{cache_key}.pkl")

        if os.path.exists(cache_file):
            with open(cache_file, 'rb') as f:
                cache_data = pickle.load(f)
            return cache_data

        return None
    except Exception as e:
        return None

def get_all_cached_entries():
    """Get all cached entries with metadata"""
    try:
        cache_dir = get_cache_dir()
        cached_entries = []

        for filename in os.listdir(cache_dir):
            if filename.endswith('.pkl'):
                try:
                    cache_file = os.path.join(cache_dir, filename)
                    with open(cache_file, 'rb') as f:
                        cache_data = pickle.load(f)

                    # Handle both single and multi-ticker cache entries
                    cache_type = cache_data.get('type', 'single')  # Default to single for backward compatibility

                    if cache_type == 'multi':
                        # Multi-ticker entry
                        entry = {
                            'type': 'multi',
                            'tickers': cache_data['tickers'],
                            'ticker_display': ', '.join(cache_data['tickers'][:3]) + ('...' if len(cache_data['tickers']) > 3 else ''),
                            'num_years': cache_data['num_years'],
                            'start_month': cache_data['start_month'],
                            'start_day': cache_data['start_day'],
                            'end_month': cache_data['end_month'],
                            'end_day': cache_data['end_day'],
                            'timestamp': cache_data['timestamp'],
                            'cache_key': filename[:-4]  # Remove .pkl extension
                        }
                    else:
                        # Single ticker entry
                        entry = {
                            'type': 'single',
                            'ticker': cache_data['ticker'],
                            'num_years': cache_data['num_years'],
                            'start_month': cache_data['start_month'],
                            'start_day': cache_data['start_day'],
                            'end_month': cache_data['end_month'],
                            'end_day': cache_data['end_day'],
                            'timestamp': cache_data['timestamp'],
                            'cache_key': filename[:-4],  # Remove .pkl extension
                            'company_info': cache_data.get('company_info', {})  # Include company info if available
                        }

                    cached_entries.append(entry)
                except:
                    continue

        # Sort by timestamp (newest first)
        cached_entries.sort(key=lambda x: x['timestamp'], reverse=True)
        return cached_entries
    except Exception as e:
        return []

def clear_cache():
    """Clear all cached data"""
    try:
        cache_dir = get_cache_dir()
        cleared_count = 0

        for filename in os.listdir(cache_dir):
            if filename.endswith('.pkl'):
                os.remove(os.path.join(cache_dir, filename))
                cleared_count += 1

        return cleared_count
    except Exception as e:
        return 0

def delete_single_cache(cache_key):
    """Delete a single cached analysis by cache key"""
    try:
        cache_dir = get_cache_dir()
        cache_file = os.path.join(cache_dir, f"{cache_key}.pkl")

        if os.path.exists(cache_file):
            os.remove(cache_file)
            return True
        return False
    except Exception as e:
        return False

def find_best_buy_timing(years_data):
    """Find the best time to buy based on historical seasonal patterns"""
    try:
        # Collect all price data by day of year
        day_prices = {}  # day_of_year -> list of prices

        for year, data in years_data.items():
            if data is not None and not data.empty:
                for idx, row in data.iterrows():
                    day_of_year = idx.timetuple().tm_yday
                    price = row['Close']

                    if day_of_year not in day_prices:
                        day_prices[day_of_year] = []
                    day_prices[day_of_year].append(price)

        # Calculate average price for each day of year
        avg_day_prices = {}
        for day, prices in day_prices.items():
            if len(prices) >= 2:  # Need at least 2 data points
                avg_day_prices[day] = np.mean(prices)

        if not avg_day_prices:
            return None

        # Find the day with the lowest average price (best buy opportunity)
        best_buy_day = min(avg_day_prices.keys(), key=lambda d: avg_day_prices[d])

        # Convert day of year to approximate month/day
        import calendar

        # Find month and day from day of year
        for month in range(1, 13):
            days_in_month = calendar.monthrange(2024, month)[1]  # Use 2024 as reference (leap year)
            month_start = sum(calendar.monthrange(2024, m)[1] for m in range(1, month))
            month_end = month_start + days_in_month

            if month_start < best_buy_day <= month_end:
                day_in_month = best_buy_day - month_start
                return {
                    'day_of_year': best_buy_day,
                    'month': month,
                    'day': day_in_month,
                    'avg_price': avg_day_prices[best_buy_day],
                    'month_name': calendar.month_name[month]
                }

        return None
    except Exception as e:
        return None

class KalmanFilter:
    """Simple Kalman Filter for stock price prediction and trend analysis"""

    def __init__(self, process_variance=1e-5, measurement_variance=1e-1, estimation_error=1.0):
        """
        Initialize Kalman Filter

        Args:
            process_variance: How much the process varies (Q)
            measurement_variance: How much measurement noise (R)
            estimation_error: Initial estimation error (P)
        """
        self.process_variance = process_variance  # Q
        self.measurement_variance = measurement_variance  # R
        self.estimation_error = estimation_error  # P
        self.state_estimate = 0.0  # x
        self.kalman_gain = 0.0  # K

    def update(self, measurement):
        """Update the Kalman filter with a new measurement"""
        # Prediction step
        # (No state transition in this simple model)
        predicted_error = self.estimation_error + self.process_variance

        # Update step
        self.kalman_gain = predicted_error / (predicted_error + self.measurement_variance)
        self.state_estimate = self.state_estimate + self.kalman_gain * (measurement - self.state_estimate)
        self.estimation_error = (1 - self.kalman_gain) * predicted_error

        return self.state_estimate

def apply_kalman_filter(prices, process_var=1e-5, measurement_var=1e-1):
    """Apply Kalman filter to a series of prices"""
    if len(prices) == 0:
        return []

    kf = KalmanFilter(process_variance=process_var, measurement_variance=measurement_var)
    kf.state_estimate = prices[0]  # Initialize with first price

    filtered_prices = []
    for price in prices:
        filtered_price = kf.update(price)
        filtered_prices.append(filtered_price)

    return filtered_prices

def create_kalman_filter_chart(ticker, years_data, show_confidence_bands=True):
    """Create Kalman filter visualization for stock price prediction and trend analysis"""
    fig = go.Figure()

    # Combine all data into a continuous timeline
    all_dates = []
    all_prices = []

    # Sort years and combine data
    sorted_years = sorted(years_data.keys())
    for year in sorted_years:
        data = years_data[year]
        if data is not None and not data.empty:
            all_dates.extend(data.index.tolist())
            all_prices.extend(data['Close'].tolist())

    if not all_dates or not all_prices:
        return None

    # Sort by date to ensure proper timeline
    combined_data = list(zip(all_dates, all_prices))
    combined_data.sort(key=lambda x: x[0])
    all_dates, all_prices = zip(*combined_data)

    # Apply Kalman filter with different parameters
    kalman_smooth = apply_kalman_filter(all_prices, process_var=1e-6, measurement_var=1e-1)
    kalman_responsive = apply_kalman_filter(all_prices, process_var=1e-4, measurement_var=1e-1)

    # Calculate prediction confidence bands
    residuals = np.array(all_prices) - np.array(kalman_smooth)
    std_residual = np.std(residuals)

    upper_band = np.array(kalman_smooth) + 2 * std_residual
    lower_band = np.array(kalman_smooth) - 2 * std_residual

    # Add confidence bands if requested
    if show_confidence_bands:
        # Upper confidence band (invisible line for fill)
        fig.add_trace(go.Scatter(
            x=all_dates,
            y=upper_band,
            mode='lines',
            line=dict(color='rgba(0, 0, 0, 0)'),
            showlegend=False,
            hoverinfo='skip'
        ))

        # Lower confidence band with fill
        fig.add_trace(go.Scatter(
            x=all_dates,
            y=lower_band,
            mode='lines',
            line=dict(color='rgba(0, 0, 0, 0)'),
            fill='tonexty',
            fillcolor='rgba(100, 149, 237, 0.2)',
            name='95% Confidence',
            hovertemplate='<b>Confidence Band</b><br>' +
                         'Date: %{x}<br>' +
                         'Lower: $%{y:.2f}<br>' +
                         '<extra></extra>'
        ))

    # Add original price data
    fig.add_trace(go.Scatter(
        x=all_dates,
        y=all_prices,
        mode='lines',
        name='Actual Price',
        line=dict(color='lightgray', width=1, dash='dot'),
        opacity=0.6,
        hovertemplate='<b>Actual Price</b><br>' +
                     'Date: %{x}<br>' +
                     'Price: $%{y:.2f}<br>' +
                     '<extra></extra>'
    ))

    # Add Kalman filtered trends
    fig.add_trace(go.Scatter(
        x=all_dates,
        y=kalman_smooth,
        mode='lines',
        name='Kalman Smooth',
        line=dict(color='#1f77b4', width=3),
        hovertemplate='<b>Kalman Smooth Trend</b><br>' +
                     'Date: %{x}<br>' +
                     'Filtered Price: $%{y:.2f}<br>' +
                     '<extra></extra>'
    ))

    fig.add_trace(go.Scatter(
        x=all_dates,
        y=kalman_responsive,
        mode='lines',
        name='Kalman Responsive',
        line=dict(color='#ff7f0e', width=2),
        hovertemplate='<b>Kalman Responsive Trend</b><br>' +
                     'Date: %{x}<br>' +
                     'Filtered Price: $%{y:.2f}<br>' +
                     '<extra></extra>'
    ))

    # Calculate trend direction indicators
    recent_smooth = kalman_smooth[-30:] if len(kalman_smooth) >= 30 else kalman_smooth
    trend_slope = (recent_smooth[-1] - recent_smooth[0]) / len(recent_smooth) if len(recent_smooth) > 1 else 0

    # Add trend direction annotation
    trend_direction = "📈 Uptrend" if trend_slope > 0 else "📉 Downtrend" if trend_slope < 0 else "➡️ Sideways"
    trend_color = "green" if trend_slope > 0 else "red" if trend_slope < 0 else "gray"

    fig.add_annotation(
        x=all_dates[-1],
        y=kalman_smooth[-1],
        #text=f"{trend_direction}<br>Slope: {trend_slope:.3f}",
        showarrow=True,
        arrowhead=2,
        arrowcolor=trend_color,
        #bgcolor="white",
        #bordercolor=trend_color,
        #borderwidth=2
    )

    fig.update_layout(
        title=f'Kalman Filter Analysis for {ticker}',
        xaxis_title='Date',
        yaxis_title='Stock Price ($)',
        hovermode='closest',
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="center",
            x=0.5
        ),
        height=600
    )

    # Format x-axis to show dates nicely
    fig.update_xaxes(
        tickformat='%Y-%m',
        dtick='M3'  # Show every 3 months
    )

    # Add vertical line for today's date
    today = datetime.now()
    fig.add_shape(
        type="line",
        x0=today, x1=today,
        y0=0, y1=1,
        yref="paper",
        line=dict(color='gold', width=2, dash='solid')
    )

    # Add annotation for today's date
    fig.add_annotation(
        x=today,
        y=1,
        yref="paper",
        text=f"Today ({today.strftime('%b %d')})",
        showarrow=True,
        arrowhead=2,
        arrowcolor='gold',
        bgcolor='rgba(255, 255, 255, 0.8)',
        bordercolor='gold',
        borderwidth=1,
        font=dict(color='gold', size=10)
    )

    return fig

def normalize_canadian_ticker(ticker):
    """Normalize Canadian ticker symbols for different exchanges"""
    ticker = ticker.upper().strip()

    # Canadian exchange mappings
    canadian_exchanges = {
        'TSX': '.TO',      # Toronto Stock Exchange
        'TSXV': '.V',      # TSX Venture Exchange
        'CSE': '.CN',      # Canadian Securities Exchange
        'NEO': '.NE'       # NEO Exchange
    }

    # If ticker already has Canadian suffix, return as-is
    for suffix in canadian_exchanges.values():
        if ticker.endswith(suffix):
            return ticker

    # Common Canadian stocks that should use .TO by default
    major_canadian_stocks = {
        'SHOP', 'RY', 'TD', 'BNS', 'BMO', 'CM', 'CNR', 'CP', 'ENB', 'TRP',
        'SU', 'CNQ', 'IMO', 'CVE', 'WCP', 'WEED', 'ACB', 'HEXO', 'OGI',
        'BB', 'NTAR', 'LSPD', 'DOC', 'WELL', 'CTS', 'FOOD', 'TOI', 'REAL',
        'BAM', 'BIP', 'BEP', 'BBU', 'BIPC', 'BEPC', 'FFH', 'IFC', 'MFC',
        'SLF', 'GWO', 'POW', 'IAG', 'EMA', 'FTS', 'H', 'CU', 'AQN', 'BHC'
    }

    # If it's a known Canadian stock, add .TO suffix
    if ticker in major_canadian_stocks:
        return f"{ticker}.TO"

    # Otherwise return as-is (could be US stock or other)
    return ticker

def try_canadian_variations(base_ticker):
    """Generate Canadian ticker variations to try"""
    base_ticker = base_ticker.upper().strip()

    # Remove any existing suffix to get base ticker
    for suffix in ['.TO', '.V', '.CN', '.NE']:
        if base_ticker.endswith(suffix):
            base_ticker = base_ticker[:-len(suffix)]
            break

    variations = [
        base_ticker,           # Original ticker (US market)
        f"{base_ticker}.TO",   # TSX
        f"{base_ticker}.V",    # TSX Venture
        f"{base_ticker}.CN",   # Canadian Securities Exchange
        f"{base_ticker}.NE"    # NEO Exchange
    ]
    return variations

def fetch_stock_data_yahoo_direct(ticker, start_date, end_date):
    """Fetch stock data using Yahoo Finance direct API"""
    try:
        import requests
        import time

        # Convert dates to timestamps
        start_timestamp = int(start_date.timestamp())
        end_timestamp = int(end_date.timestamp())

        url = f"https://query1.finance.yahoo.com/v8/finance/chart/{ticker}"
        params = {
            "period1": start_timestamp,
            "period2": end_timestamp,
            "interval": "1d"
        }

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

        response = requests.get(url, params=params, headers=headers, timeout=15)
        data = response.json()

        if "chart" in data and data["chart"]["result"]:
            result = data["chart"]["result"][0]
            timestamps = result["timestamp"]
            quotes = result["indicators"]["quote"][0]

            # Convert to DataFrame
            dates = [datetime.fromtimestamp(ts) for ts in timestamps]
            df = pd.DataFrame({
                'Open': quotes.get('open', [None] * len(dates)),
                'High': quotes.get('high', [None] * len(dates)),
                'Low': quotes.get('low', [None] * len(dates)),
                'Close': quotes.get('close', [None] * len(dates)),
                'Volume': quotes.get('volume', [None] * len(dates))
            }, index=pd.DatetimeIndex(dates))

            # Remove rows with None values
            df = df.dropna()

            return df if not df.empty else None

    except Exception as e:
        return None

def fetch_stock_data_yfinance(ticker, start_date, end_date):
    """Fetch stock data using yfinance library (fallback)"""
    try:
        import time
        time.sleep(0.1)

        stock = yf.Ticker(ticker)
        data = stock.history(start=start_date, end=end_date, auto_adjust=True)

        return data if not data.empty else None

    except Exception as e:
        return None

def generate_demo_data(ticker, start_date, end_date):
    """Generate realistic demo stock data"""
    try:
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        dates = dates[dates.weekday < 5]  # Only weekdays

        if len(dates) == 0:
            return None

        # Generate realistic stock price movement
        np.random.seed(hash(ticker) % 1000)  # Consistent data for same ticker

        base_price = 100 + (hash(ticker) % 200)  # Base price between 100-300
        returns = np.random.normal(0.001, 0.02, len(dates))  # Daily returns

        # Add seasonal patterns
        for i, date in enumerate(dates):
            if date.month in [11, 12]:  # Holiday season boost
                returns[i] += 0.002
            elif date.month in [7, 8]:  # Summer slowdown
                returns[i] -= 0.001

        prices = [base_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))

        data = pd.DataFrame({
            'Open': [p * 0.99 for p in prices],
            'High': [p * 1.02 for p in prices],
            'Low': [p * 0.98 for p in prices],
            'Close': prices,
            'Volume': [random.randint(1000000, 10000000) for _ in prices]
        }, index=dates)

        return data

    except Exception as e:
        return None

def fetch_stock_data(ticker, start_date, end_date):
    """Fetch stock data with multiple fallback methods and Canadian exchange support"""

    # Method 1: Try normalized ticker first
    normalized_ticker = normalize_canadian_ticker(ticker)
    data = fetch_stock_data_yahoo_direct(normalized_ticker, start_date, end_date)
    if data is not None and not data.empty:
        return data

    # Method 2: Try all Canadian variations if normalized ticker failed
    if normalized_ticker == ticker:  # Only try variations if normalization didn't change anything
        variations = try_canadian_variations(ticker)
        for variation in variations:
            if variation != ticker:  # Skip the original ticker since we already tried it
                data = fetch_stock_data_yahoo_direct(variation, start_date, end_date)
                if data is not None and not data.empty:
                    return data

    # Method 3: yfinance library fallback with Canadian support
    data = fetch_stock_data_yfinance(normalized_ticker, start_date, end_date)
    if data is not None and not data.empty:
        return data

    # Method 4: Try yfinance with variations
    if normalized_ticker == ticker:
        variations = try_canadian_variations(ticker)
        for variation in variations:
            if variation != ticker:
                data = fetch_stock_data_yfinance(variation, start_date, end_date)
                if data is not None and not data.empty:
                    return data

    # Method 5: Demo data (last resort for testing)
    if ticker.upper() in ['DEMO', 'TEST']:
        return generate_demo_data(ticker, start_date, end_date)

    return None

def normalize_to_day_of_year(df, year):
    """Convert dates to day of year for seasonal comparison"""
    df_copy = df.copy()
    df_copy['day_of_year'] = df_copy.index.dayofyear
    df_copy['year'] = year
    return df_copy

def generate_distinct_colors(num_colors):
    """Generate very distinct colors for each line"""
    # Predefined distinct colors that are easily distinguishable
    distinct_colors = [
        '#1f77b4',  # Blue
        '#ff7f0e',  # Orange
        '#2ca02c',  # Green
        '#d62728',  # Red
        '#9467bd',  # Purple
        '#8c564b',  # Brown
        '#e377c2',  # Pink
        '#7f7f7f',  # Gray
        '#bcbd22',  # Olive
        '#17becf',  # Cyan
        '#ff9999',  # Light Red
        '#66b3ff',  # Light Blue
        '#99ff99',  # Light Green
        '#ffcc99',  # Light Orange
        '#ff99cc',  # Light Pink
        '#c2c2f0',  # Light Purple
        '#ffb3e6',  # Light Magenta
        '#c4e17f',  # Light Lime
        '#76d7c4',  # Light Teal
        '#f7dc6f'   # Light Yellow
    ]

    # Return the required number of colors, cycling if needed
    return [distinct_colors[i % len(distinct_colors)] for i in range(num_colors)]

def create_seasonal_price_chart(ticker, years_data, use_log_scale=False, show_only_average=False):
    """Create overlaid seasonal price chart with distinct colors and average band"""
    fig = go.Figure()

    # Sort years (oldest to newest)
    sorted_years = sorted(years_data.keys())
    num_years = len(sorted_years)

    # Generate distinct colors for each year
    colors = generate_distinct_colors(num_years)

    # Find best buy timing
    best_buy_info = find_best_buy_timing(years_data)

    # Get today's day of year for vertical line
    today = datetime.now()
    today_day_of_year = today.timetuple().tm_yday

    # Calculate average line data by month
    all_month_data = {}  # month -> list of prices

    for year in sorted_years:
        data = years_data[year]
        if data is not None and not data.empty:
            for idx, row in data.iterrows():
                # Get month from the index (date)
                month = idx.month
                price = row['Close']
                if month not in all_month_data:
                    all_month_data[month] = []
                all_month_data[month].append(price)

    # Calculate average prices for each month
    avg_months = sorted(all_month_data.keys())
    avg_prices = [np.mean(all_month_data[month]) for month in avg_months]

    # Convert months to day-of-year for plotting (use middle of month)
    month_to_day = {1: 15, 2: 45, 3: 74, 4: 105, 5: 135, 6: 166,
                    7: 196, 8: 227, 9: 258, 10: 288, 11: 319, 12: 349}
    avg_days = [month_to_day[month] for month in avg_months]

    # Smooth the average line over the entire period using monthly averages
    if len(avg_days) > 3:
        from scipy.interpolate import interp1d
        # Create a smooth interpolation over the full year range
        f = interp1d(avg_days, avg_prices, kind='cubic', fill_value='extrapolate')
        # Create smooth line covering the entire period
        smooth_days = np.linspace(1, 365, 365)  # One point per day
        smooth_prices = f(smooth_days)
    else:
        smooth_days = avg_days
        smooth_prices = avg_prices

    # Add average band (semi-transparent and smooth)
    avg_color = 'rgba(128, 128, 128, 0.8)' if not show_only_average else 'rgba(31, 119, 180, 1.0)'
    avg_width = 4 if not show_only_average else 3

    fig.add_trace(go.Scatter(
        x=smooth_days,
        y=smooth_prices,
        mode='lines',
        name='Average',
        line=dict(color=avg_color, width=avg_width, shape='spline'),
        fill=None,
        hovertemplate='<b>Average</b><br>' +
                     'Day of Year: %{x:.0f}<br>' +
                     'Avg Price: $%{y:.2f}<br>' +
                     '<extra></extra>'
    ))

    # Add individual year lines (only if not showing average only)
    if not show_only_average:
        current_year = datetime.now().year
        last_year = current_year - 1

        for i, year in enumerate(sorted_years):
            data = years_data[year]
            if data is not None and not data.empty:
                # Determine visibility: show current year, last year, hide others
                visible = True if year in [current_year, last_year] else 'legendonly'

                fig.add_trace(go.Scatter(
                    x=data['day_of_year'],
                    y=data['Close'],
                    mode='lines',
                    name=f'{year}',
                    line=dict(color=colors[i], width=2),
                    visible=visible,
                    hovertemplate=f'<b>{year}</b><br>' +
                                 'Day of Year: %{x}<br>' +
                                 'Price: $%{y:.2f}<br>' +
                                 '<extra></extra>'
                ))

    y_axis_type = 'log' if use_log_scale else 'linear'
    y_axis_title = f'Stock Price ($) - {"Log Scale" if use_log_scale else "Linear Scale"}'

    fig.update_layout(
        title=f'Seasonal Price Patterns for {ticker}',
        xaxis_title='Day of Year',
        yaxis_title=y_axis_title,
        yaxis_type=y_axis_type,
        hovermode='closest',  # Changed from 'x unified' to show only hovered line
        legend=dict(
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.02
        ),
        height=600
    )

    # Add month markers
    month_days = [1, 32, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335]
    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

    fig.update_layout(
        xaxis=dict(
            tickmode='array',
            tickvals=month_days,
            ticktext=month_names
        )
    )

    # Add best buy timing indicator (yellow diamond)
    if best_buy_info:
        # Find the price at the best buy day from the average line
        best_day = best_buy_info['day_of_year']

        # Find the closest point on the smooth average line
        if len(smooth_days) > 0 and len(smooth_prices) > 0:
            # Find the index of the closest day
            closest_idx = min(range(len(smooth_days)), key=lambda i: abs(smooth_days[i] - best_day))
            best_price = smooth_prices[closest_idx]

            # Add yellow diamond marker
            fig.add_trace(go.Scatter(
                x=[best_day],
                y=[best_price],
                mode='markers',
                marker=dict(
                    symbol='diamond',
                    size=15,
                    color='gold',
                    line=dict(color='orange', width=2)
                ),
                name=f'Best Buy Time',
                hovertemplate=f'<b>Best Buy Opportunity</b><br>' +
                             f'Day of Year: {best_day}<br>' +
                             f'Date: {best_buy_info["month_name"]} {best_buy_info["day"]}<br>' +
                             f'Avg Price: ${best_price:.2f}<br>' +
                             '<extra></extra>',
                showlegend=True
            ))

    # Add vertical line for today's date
    fig.add_vline(
        x=today_day_of_year,
        line=dict(color='gold', width=2, dash='solid'),
        annotation_text=f"Today ({today.strftime('%b %d')})",
        annotation_position="top",
        annotation=dict(
            font=dict(color='gold', size=10),
            bgcolor='black',
            bordercolor='gold',
            borderwidth=1
        )
    )

    return fig

def create_multi_year_line_chart(ticker, years_data, num_years_to_show=5):
    """Create a continuous line chart showing multiple years of price data"""
    fig = go.Figure()

    # Sort years (newest to oldest) and take the requested number
    sorted_years = sorted(years_data.keys(), reverse=True)[:num_years_to_show]
    sorted_years = sorted(sorted_years)  # Re-sort oldest to newest for display

    # Combine all data into a continuous timeline
    all_dates = []
    all_prices = []

    for year in sorted_years:
        data = years_data[year]
        if data is not None and not data.empty:
            # Use actual dates instead of day_of_year for continuous timeline
            all_dates.extend(data.index.tolist())
            all_prices.extend(data['Close'].tolist())

    if all_dates and all_prices:
        # Sort by date to ensure proper timeline
        combined_data = list(zip(all_dates, all_prices))
        combined_data.sort(key=lambda x: x[0])
        all_dates, all_prices = zip(*combined_data)

        # Create single continuous line
        fig.add_trace(go.Scatter(
            x=all_dates,
            y=all_prices,
            mode='lines',
            name=f'{ticker} Price',
            line=dict(color='#1f77b4', width=2),
            hovertemplate='<b>%{x}</b><br>' +
                         'Price: $%{y:.2f}<br>' +
                         '<extra></extra>'
        ))

    fig.update_layout(
        title=f'{ticker} Price History - Last {len(sorted_years)} Years',
        xaxis_title='Date',
        yaxis_title='Stock Price ($)',
        hovermode='closest',
        height=600,
        showlegend=False  # Single line doesn't need legend
    )

    # Format x-axis to show dates nicely
    fig.update_xaxes(
        tickformat='%Y-%m',
        dtick='M3'  # Show every 3 months
    )

    # Add vertical line for today's date using shapes (more reliable for datetime x-axis)
    today = datetime.now()
    fig.add_shape(
        type="line",
        x0=today, x1=today,
        y0=0, y1=1,
        yref="paper",
        line=dict(color='gold', width=2, dash='solid')
    )

    # Add annotation for today's date
    fig.add_annotation(
        x=today,
        y=1,
        yref="paper",
        text=f"Today ({today.strftime('%b %d')})",
        showarrow=True,
        arrowhead=2,
        arrowcolor='gold',
        bgcolor='rgba(255, 255, 255, 0.8)',
        bordercolor='gold',
        borderwidth=1,
        font=dict(color='gold', size=10)
    )

    return fig

def create_recent_comparison_chart(ticker, years_data):
    """Create comparison chart for last year vs this year only"""
    fig = go.Figure()

    # Get current year and last year
    current_year = datetime.now().year
    last_year = current_year - 1

    # Colors for the two years
    colors = ['#d62728', '#1f77b4']  # Red for last year, Blue for this year
    year_labels = [f'{last_year} (Last Year)', f'{current_year} (This Year)']

    years_to_show = [last_year, current_year]

    # Get today's day of year for vertical line
    today = datetime.now()
    today_day_of_year = today.timetuple().tm_yday

    for i, year in enumerate(years_to_show):
        if year in years_data:
            data = years_data[year]
            if data is not None and not data.empty:
                # Calculate cumulative returns from start of period
                start_price = data['Close'].iloc[0]
                returns = ((data['Close'] / start_price) - 1) * 100

                fig.add_trace(go.Scatter(
                    x=data['day_of_year'],
                    y=returns,
                    mode='lines',
                    name=year_labels[i],
                    line=dict(color=colors[i], width=3),  # Thicker lines for emphasis
                    hovertemplate=f'<b>{year}</b><br>' +
                                 'Day of Year: %{x}<br>' +
                                 'Return: %{y:.2f}%<br>' +
                                 '<extra></extra>'
                ))

    fig.update_layout(
        title=f'Recent Year Comparison for {ticker}',
        xaxis_title='Day of Year',
        yaxis_title='Cumulative Return (%)',
        hovermode='closest',
        legend=dict(
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.02
        ),
        height=500
    )

    # Add month markers
    month_days = [1, 32, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335]
    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

    fig.update_layout(
        xaxis=dict(
            tickmode='array',
            tickvals=month_days,
            ticktext=month_names
        )
    )

    # Add horizontal line at 0%
    fig.add_hline(y=0, line_dash="dash", line_color="gray", opacity=0.5)

    # Add vertical line for today's date
    fig.add_vline(
        x=today_day_of_year,
        line=dict(color='gold', width=2, dash='solid'),
        annotation_text=f"Today ({today.strftime('%b %d')})",
        annotation_position="top",
        annotation=dict(
            font=dict(color='gold', size=10),
            bgcolor='black',
            bordercolor='gold',
            borderwidth=1
        )
    )

    return fig

def create_normalized_seasonal_chart(ticker, years_data, show_only_average=False):
    """Create seasonal chart with all prices normalized to start at 100"""
    fig = go.Figure()

    # Sort years (oldest to newest)
    sorted_years = sorted(years_data.keys())
    num_years = len(sorted_years)

    # Generate distinct colors for each year
    colors = generate_distinct_colors(num_years)

    # Get today's day of year for vertical line
    today = datetime.now()
    today_day_of_year = today.timetuple().tm_yday

    # Calculate normalized average data by month
    all_month_normalized = {}  # month -> list of normalized prices

    for year in sorted_years:
        data = years_data[year]
        if data is not None and not data.empty:
            # Normalize to start at 100
            start_price = data['Close'].iloc[0]
            normalized_prices = (data['Close'] / start_price) * 100

            for idx, norm_price in enumerate(normalized_prices):
                # Get month from the index (date)
                month = data.index[idx].month
                if month not in all_month_normalized:
                    all_month_normalized[month] = []
                all_month_normalized[month].append(norm_price)

    # Calculate average normalized prices and standard deviations for each month
    avg_months = sorted(all_month_normalized.keys())
    avg_normalized = [np.mean(all_month_normalized[month]) for month in avg_months]
    std_normalized = [np.std(all_month_normalized[month]) for month in avg_months]

    # Convert months to day-of-year for plotting (use middle of month)
    month_to_day = {1: 15, 2: 45, 3: 74, 4: 105, 5: 135, 6: 166,
                    7: 196, 8: 227, 9: 258, 10: 288, 11: 319, 12: 349}
    avg_days = [month_to_day[month] for month in avg_months]

    # Smooth the average and std dev lines over the entire period using monthly data
    if len(avg_days) > 3:
        from scipy.interpolate import interp1d
        # Create smooth interpolations over the full year range
        f_avg = interp1d(avg_days, avg_normalized, kind='cubic', fill_value='extrapolate')
        f_std = interp1d(avg_days, std_normalized, kind='cubic', fill_value='extrapolate')
        # Create smooth lines covering the entire period
        smooth_days = np.linspace(1, 365, 365)  # One point per day
        smooth_normalized = f_avg(smooth_days)
        smooth_std_norm = f_std(smooth_days)
    else:
        smooth_days = avg_days
        smooth_normalized = avg_normalized
        smooth_std_norm = std_normalized

    # Add standard deviation bands (upper and lower)
    upper_band = smooth_normalized + smooth_std_norm
    lower_band = smooth_normalized - smooth_std_norm

    # Add upper std dev band (invisible line for fill)
    fig.add_trace(go.Scatter(
        x=smooth_days,
        y=upper_band,
        mode='lines',
        line=dict(color='rgba(128, 128, 128, 0)'),  # Invisible line
        showlegend=False,
        hoverinfo='skip'
    ))

    # Add lower std dev band with fill to upper band
    fig.add_trace(go.Scatter(
        x=smooth_days,
        y=lower_band,
        mode='lines',
        line=dict(color='rgba(128, 128, 128, 0)'),  # Invisible line
        fill='tonexty',  # Fill to previous trace (upper band)
        fillcolor='rgba(128, 128, 128, 0.2)',  # Semi-transparent gray
        name='±1 Std Dev',
        hovertemplate='<b>Standard Deviation Band</b><br>' +
                     'Day of Year: %{x:.0f}<br>' +
                     'Lower: %{y:.1f}<br>' +
                     '<extra></extra>'
    ))

    # Add average line
    avg_color = 'rgba(128, 128, 128, 0.8)' if not show_only_average else 'rgba(31, 119, 180, 1.0)'
    avg_width = 4 if not show_only_average else 3

    fig.add_trace(go.Scatter(
        x=smooth_days,
        y=smooth_normalized,
        mode='lines',
        name='Average',
        line=dict(color=avg_color, width=avg_width, shape='spline'),
        fill=None,
        hovertemplate='<b>Average</b><br>' +
                     'Day of Year: %{x:.0f}<br>' +
                     'Normalized: %{y:.1f}<br>' +
                     'Std Dev: %{customdata:.1f}<br>' +
                     '<extra></extra>',
        customdata=smooth_std_norm
    ))

    # Add individual year lines (only if not showing average only)
    if not show_only_average:
        current_year = datetime.now().year
        last_year = current_year - 1

        for i, year in enumerate(sorted_years):
            data = years_data[year]
            if data is not None and not data.empty:
                # Normalize to start at 100
                start_price = data['Close'].iloc[0]
                normalized_prices = (data['Close'] / start_price) * 100

                # Determine visibility: show current year, last year, hide others
                visible = True if year in [current_year, last_year] else 'legendonly'

                fig.add_trace(go.Scatter(
                    x=data['day_of_year'],
                    y=normalized_prices,
                    mode='lines',
                    name=f'{year}',
                    line=dict(color=colors[i], width=2),
                    visible=visible,
                    hovertemplate=f'<b>{year}</b><br>' +
                                 'Day of Year: %{x}<br>' +
                                 'Normalized: %{y:.1f}<br>' +
                                 '<extra></extra>'
                ))

    fig.update_layout(
        title=f'Normalized Seasonal Patterns for {ticker} (Starting at 100)',
        xaxis_title='Day of Year',
        yaxis_title='Normalized Price (Start = 100)',
        hovermode='closest',
        legend=dict(
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.02
        ),
        height=600
    )

    # Add month markers
    month_days = [1, 32, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335]
    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

    fig.update_layout(
        xaxis=dict(
            tickmode='array',
            tickvals=month_days,
            ticktext=month_names
        )
    )

    # Add horizontal line at 100 (starting point)
    fig.add_hline(y=100, line_dash="dash", line_color="gray", opacity=0.5)

    # Add vertical line for today's date
    fig.add_vline(
        x=today_day_of_year,
        line=dict(color='gold', width=2, dash='solid'),
        annotation_text=f"Today ({today.strftime('%b %d')})",
        annotation_position="top",
        annotation=dict(
            font=dict(color='gold', size=10),
            bgcolor='black',
            bordercolor='gold',
            borderwidth=1
        )
    )

    return fig

def calculate_rsi(prices, period=14):
    """Calculate Relative Strength Index (RSI)"""
    if len(prices) < period + 1:
        return pd.Series([50] * len(prices), index=prices.index)

    # Calculate price changes
    delta = prices.diff()

    # Separate gains and losses
    gains = delta.where(delta > 0, 0)
    losses = -delta.where(delta < 0, 0)

    # Calculate average gains and losses
    avg_gains = gains.rolling(window=period).mean()
    avg_losses = losses.rolling(window=period).mean()

    # Calculate RS and RSI
    rs = avg_gains / avg_losses
    rsi = 100 - (100 / (1 + rs))

    # Fill NaN values with 50 (neutral)
    rsi = rsi.fillna(50)

    return rsi

def create_rsi_chart(ticker, years_data, show_only_average=False):
    """Create RSI chart showing seasonal RSI patterns"""
    fig = go.Figure()

    # Sort years (oldest to newest)
    sorted_years = sorted(years_data.keys())
    num_years = len(sorted_years)

    # Generate distinct colors for each year
    colors = generate_distinct_colors(num_years)

    # Get today's day of year for vertical line
    today = datetime.now()
    today_day_of_year = today.timetuple().tm_yday

    # Calculate RSI for each year and collect monthly averages
    all_month_rsi = {}  # month -> list of RSI values

    for year in sorted_years:
        data = years_data[year]
        if data is not None and not data.empty:
            # Calculate RSI for this year
            rsi_values = calculate_rsi(data['Close'])

            # Add RSI column to data
            data_with_rsi = data.copy()
            data_with_rsi['RSI'] = rsi_values

            # Collect monthly RSI data
            for idx, row in data_with_rsi.iterrows():
                month = idx.month
                rsi_val = row['RSI']
                if month not in all_month_rsi:
                    all_month_rsi[month] = []
                all_month_rsi[month].append(rsi_val)

    # Calculate average RSI and standard deviation for each month
    avg_months = sorted(all_month_rsi.keys())
    avg_rsi = [np.mean(all_month_rsi[month]) for month in avg_months]
    std_rsi = [np.std(all_month_rsi[month]) for month in avg_months]

    # Convert months to day-of-year for plotting
    month_to_day = {1: 15, 2: 45, 3: 74, 4: 105, 5: 135, 6: 166,
                    7: 196, 8: 227, 9: 258, 10: 288, 11: 319, 12: 349}
    avg_days = [month_to_day[month] for month in avg_months]

    # Smooth the average RSI line
    if len(avg_days) > 3:
        from scipy.interpolate import interp1d
        f_avg = interp1d(avg_days, avg_rsi, kind='cubic', fill_value='extrapolate')
        f_std = interp1d(avg_days, std_rsi, kind='cubic', fill_value='extrapolate')
        smooth_days = np.linspace(1, 365, 365)
        smooth_rsi = f_avg(smooth_days)
        smooth_std_rsi = f_std(smooth_days)
    else:
        smooth_days = avg_days
        smooth_rsi = avg_rsi
        smooth_std_rsi = std_rsi

    # Add standard deviation bands
    upper_band = smooth_rsi + smooth_std_rsi
    lower_band = smooth_rsi - smooth_std_rsi

    # Clip bands to RSI range (0-100)
    upper_band = np.clip(upper_band, 0, 100)
    lower_band = np.clip(lower_band, 0, 100)

    # Add upper std dev band (invisible line for fill)
    fig.add_trace(go.Scatter(
        x=smooth_days,
        y=upper_band,
        mode='lines',
        line=dict(color='rgba(128, 128, 128, 0)'),
        showlegend=False,
        hoverinfo='skip'
    ))

    # Add lower std dev band with fill
    fig.add_trace(go.Scatter(
        x=smooth_days,
        y=lower_band,
        mode='lines',
        line=dict(color='rgba(128, 128, 128, 0)'),
        fill='tonexty',
        fillcolor='rgba(128, 128, 128, 0.2)',
        name='±1 Std Dev',
        hovertemplate='<b>RSI Standard Deviation Band</b><br>' +
                     'Day of Year: %{x:.0f}<br>' +
                     'Lower RSI: %{y:.1f}<br>' +
                     '<extra></extra>'
    ))

    # Add average RSI line
    avg_color = 'rgba(128, 128, 128, 0.8)' if not show_only_average else 'rgba(31, 119, 180, 1.0)'
    avg_width = 4 if not show_only_average else 3

    fig.add_trace(go.Scatter(
        x=smooth_days,
        y=smooth_rsi,
        mode='lines',
        name='Average RSI',
        line=dict(color=avg_color, width=avg_width, shape='spline'),
        hovertemplate='<b>Average RSI</b><br>' +
                     'Day of Year: %{x:.0f}<br>' +
                     'RSI: %{y:.1f}<br>' +
                     'Std Dev: %{customdata:.1f}<br>' +
                     '<extra></extra>',
        customdata=smooth_std_rsi
    ))

    # Add individual year RSI lines (only if not showing average only)
    if not show_only_average:
        current_year = datetime.now().year
        last_year = current_year - 1

        for i, year in enumerate(sorted_years):
            data = years_data[year]
            if data is not None and not data.empty:
                rsi_values = calculate_rsi(data['Close'])

                # Determine visibility: show current year, last year, hide others
                visible = True if year in [current_year, last_year] else 'legendonly'

                fig.add_trace(go.Scatter(
                    x=data['day_of_year'],
                    y=rsi_values,
                    mode='lines',
                    name=f'{year}',
                    line=dict(color=colors[i], width=2),
                    visible=visible,
                    hovertemplate=f'<b>{year}</b><br>' +
                                 'Day of Year: %{x}<br>' +
                                 'RSI: %{y:.1f}<br>' +
                                 '<extra></extra>'
                ))

    # Add RSI reference lines
    fig.add_hline(y=70, line_dash="dash", line_color="red", opacity=0.7,
                  annotation_text="Overbought (70)", annotation_position="bottom right")
    fig.add_hline(y=30, line_dash="dash", line_color="green", opacity=0.7,
                  annotation_text="Oversold (30)", annotation_position="top right")
    fig.add_hline(y=50, line_dash="dot", line_color="gray", opacity=0.5,
                  annotation_text="Neutral (50)", annotation_position="top left")

    fig.update_layout(
        title=f'Seasonal RSI Patterns for {ticker}',
        xaxis_title='Day of Year',
        yaxis_title='RSI (Relative Strength Index)',
        yaxis=dict(range=[0, 100]),
        hovermode='closest',
        legend=dict(
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.02
        ),
        height=500
    )

    # Add month markers
    month_days = [1, 32, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335]
    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

    fig.update_layout(
        xaxis=dict(
            tickmode='array',
            tickvals=month_days,
            ticktext=month_names
        )
    )

    # Add vertical line for today's date
    fig.add_vline(
        x=today_day_of_year,
        line=dict(color='gold', width=2, dash='solid'),
        annotation_text=f"Today ({today.strftime('%b %d')})",
        annotation_position="top",
        annotation=dict(
            font=dict(color='gold', size=10),
            bgcolor='black',
            bordercolor='gold',
            borderwidth=1
        )
    )

    return fig

def get_ticker_specific_events(ticker, year):
    """Get ticker-specific events and volatility drivers for a given year"""

    # Define ticker-specific events
    ticker_events = {
        'AAPL': {
            2024: "EARNINGS: Q4 revenue $94.9B (+6% YoY), iPhone revenue $46.2B (+6%), Services $25B (+12%)\nPRODUCT LAUNCHES: iPhone 16 Pro with A18 Pro chip (Sep), Apple Intelligence features rollout\nMANAGEMENT COMMENTARY: 'iPhone 16 designed for Apple Intelligence from ground up' - Tim Cook\nKEY METRICS: China revenue $15B (-0.3%), Wearables $9B (+1%), Mac $7.7B (+2%)\nOUTLOOK: Services growth acceleration expected, AI features driving upgrade cycle",
            2023: "EARNINGS: Q4 revenue $89.5B (-1% YoY), iPhone revenue $43.8B (+3%), Services $22.3B (+16%)\nPRODUCT LAUNCHES: iPhone 15 with USB-C (Sep), Vision Pro unveiled at WWDC (Jun)\nMANAGEMENT COMMENTARY: 'Vision Pro represents entirely new category' - Tim Cook\nKEY METRICS: China revenue $15.1B (-2.5%), Active devices 2.2B, Services attach rate 31%\nOUTLOOK: Vision Pro launch 2024, Services momentum continues, China stabilization",
            2022: "• iPhone 14 launch (Sep)\n• Q4 earnings miss (Oct)\n• Supply chain recovery\n• Services growth 14%",
            2021: "• iPhone 13 launch (Sep)\n• Q1 record $123B revenue\n• M1 MacBook Pro launch\n• Stock split 4:1 (Aug)",
            2020: "• iPhone 12 launch delayed (Oct)\n• Q1 record $111B revenue\n• Work-from-home Mac surge\n• Stock split 4:1 (Aug)",
            2019: "• iPhone 11 launch (Sep)\n• Q4 earnings beat (Oct)\n• Services revenue $46B\n• AirPods Pro launch",
            2018: "• iPhone XS/XR launch (Sep)\n• Q4 revenue guidance cut\n• China sales decline\n• Services growth slows",
            2017: "• iPhone X launch (Nov)\n• Q1 record $88B revenue\n• $1T market cap approach\n• Strong iPhone upgrade cycle",
            2016: "• iPhone 7 launch (Sep)\n• First annual revenue decline\n• Q4 earnings miss\n• Services growth 24%",
            2015: "• iPhone 6s launch (Sep)\n• Apple Watch debut (Apr)\n• Q1 record $75B revenue\n• China revenue doubles"
        },
        'MSFT': {
            2024: "EARNINGS: Q1 revenue $65.6B (+16% YoY), Azure growth 33%, Productivity revenue $28.3B (+12%)\nPRODUCT LAUNCHES: Copilot Pro subscription, Azure AI services expansion, Gaming revenue surge\nMANAGEMENT COMMENTARY: 'Copilot is becoming the UI for AI' - Satya Nadella\nKEY METRICS: Microsoft 365 Commercial seats 440M, Teams Phone 15M seats, Gaming revenue $7.1B (+61%)\nOUTLOOK: AI monetization accelerating, Azure growth sustainable, Copilot adoption expanding",
            2023: "• OpenAI partnership (Jan)\n• Q4 Azure growth 26%\n• Copilot launch (Mar)\n• Activision deal approved",
            2022: "• Q4 Azure growth 40%\n• Teams 270M users\n• Activision acquisition (Jan)\n• Cloud revenue $91B",
            2021: "• Q4 Azure growth 51%\n• Teams 250M users\n• Xbox Series X launch\n• Cloud revenue $60B",
            2020: "• Q4 Azure growth 62%\n• Teams 115M daily users\n• Surface revenue up 37%\n• Work-from-home boost",
            2019: "• Q4 Azure growth 64%\n• Office 365 200M users\n• Surface revenue up 21%\n• LinkedIn revenue $6.8B",
            2018: "• Q4 Azure growth 76%\n• Office 365 155M users\n• LinkedIn acquisition complete\n• Cloud revenue $32B",
            2017: "• Q4 Azure growth 93%\n• Office 365 100M users\n• Surface revenue up 32%\n• Cloud revenue $20B",
            2016: "• Q4 Azure growth 120%\n• Office 365 70M users\n• Surface revenue up 29%\n• Cloud transition",
            2015: "• Windows 10 launch (Jul)\n• Office 365 15M users\n• Azure revenue up 140%\n• Nadella's vision"
        },
        'GOOGL': {
            2024: "• Q3 Search revenue $49B\n• Gemini AI integration\n• Cloud revenue $11B (+35%)\n• YouTube Shorts 70B hours",
            2023: "• Q4 Search revenue $48B\n• Bard AI launch (Mar)\n• Cloud revenue $33B (+26%)\n• YouTube revenue $31B",
            2022: "• Q4 Search revenue $42B\n• YouTube revenue decline\n• Cloud revenue $26B (+32%)\n• Pixel 7 launch",
            2021: "• Q4 Search revenue $43B\n• YouTube revenue $28B\n• Cloud revenue $19B (+47%)\n• Pixel 6 launch",
            2020: "• Q4 Search revenue $32B\n• YouTube revenue $20B\n• Cloud revenue $13B (+47%)\n• Pixel 5 launch",
            2019: "• Q4 Search revenue $27B\n• YouTube revenue $15B\n• Cloud revenue $9B (+53%)\n• Pixel 4 launch",
            2018: "• Q4 Search revenue $24B\n• YouTube revenue $11B\n• Cloud revenue $6B (+76%)\n• Pixel 3 launch",
            2017: "• Q4 Search revenue $22B\n• YouTube revenue $8B\n• Cloud revenue $4B (+85%)\n• Pixel 2 launch",
            2016: "• Q4 Search revenue $19B\n• YouTube revenue $6B\n• Cloud revenue $2B\n• Pixel phone launch",
            2015: "• Q4 Search revenue $16B\n• Alphabet restructuring (Aug)\n• YouTube Red launch\n• Cloud platform launch"
        },
        'TSLA': {
            2024: "• Q3 deliveries 463K (+6%)\n• Cybertruck production ramp\n• FSD v12 release\n• Energy storage 20.3 GWh",
            2023: "• Q4 deliveries 484K (+20%)\n• Price cuts throughout year\n• Cybertruck delivery event\n• FSD beta 11.4 release",
            2022: "• Q4 deliveries 405K (+31%)\n• Shanghai factory shutdown\n• Austin/Berlin ramp\n• Stock split 3:1 (Aug)",
            2021: "• Q4 deliveries 308K (+87%)\n• S&P 500 inclusion (Dec)\n• Model S Plaid launch\n• FSD beta rollout",
            2020: "• Q4 deliveries 180K (+61%)\n• S&P 500 inclusion announced\n• Battery Day (Sep)\n• Stock split 5:1 (Aug)",
            2019: "• Q4 deliveries 112K (+23%)\n• Shanghai factory opening\n• Cybertruck unveiling (Nov)\n• Model Y unveiling",
            2018: "• Q4 deliveries 90K (+83%)\n• Model 3 production ramp\n• SEC settlement $20M\n• Going private tweet (Aug)",
            2017: "• Q4 deliveries 29K (+27%)\n• Model 3 production start\n• SolarCity merger complete\n• Gigafactory 1 opening",
            2016: "• Q4 deliveries 22K (+73%)\n• Model 3 unveiling (Mar)\n• SolarCity acquisition\n• Autopilot 2.0 launch",
            2015: "• Q4 deliveries 17K (+75%)\n• Model X launch (Sep)\n• Gigafactory construction\n• Autopilot 1.0 launch"
        },
        'AMZN': {
            2024: "• Q3 AWS revenue $27B (+19%)\n• Prime Video ads launch\n• Q3 net income $15B\n• One Medical integration",
            2023: "• Q4 AWS revenue $24B (+13%)\n• Prime membership price hike\n• Alexa layoffs 18K\n• MGM acquisition complete",
            2022: "• Q4 AWS revenue $21B (+20%)\n• First annual loss since 2014\n• Prime price increase\n• Rivian investment loss",
            2021: "• Q4 AWS revenue $17B (+40%)\n• Prime Day record $11B\n• Bezos steps down as CEO\n• MGM acquisition $8.5B",
            2020: "• Q4 AWS revenue $13B (+28%)\n• Prime membership 200M\n• Pandemic e-commerce surge\n• Bezos sells $10B stock",
            2019: "• Q4 AWS revenue $10B (+34%)\n• One-day shipping rollout\n• HQ2 split decision\n• Advertising revenue $14B",
            2018: "• Q4 AWS revenue $7B (+45%)\n• Prime membership 100M\n• HQ2 search concludes\n• Advertising revenue $10B",
            2017: "• Q4 AWS revenue $5B (+43%)\n• Whole Foods acquisition $13.7B\n• Alexa Echo Show launch\n• Prime membership 90M",
            2016: "• Q4 AWS revenue $3.5B (+47%)\n• Prime Video global launch\n• Echo Dot launch\n• Prime membership 65M",
            2015: "• Q4 AWS revenue $2.4B (+69%)\n• Prime membership 54M\n• Echo launch\n• Fire Phone discontinued"
        }
    }

    # Get ticker-specific events or fall back to generic
    if ticker.upper() in ticker_events and year in ticker_events[ticker.upper()]:
        return ticker_events[ticker.upper()][year]
    else:
        # Generic company-focused events as fallback
        generic_events = {
            2024: "• Q3/Q4 earnings reports\n• Product launches/updates\n• Management changes\n• Strategic initiatives",
            2023: "• Annual earnings results\n• New product releases\n• Business expansion\n• Operational changes",
            2022: "• Quarterly earnings\n• Product development\n• Market expansion\n• Cost management",
            2021: "• Earnings growth\n• Product innovation\n• Market share gains\n• Digital transformation",
            2020: "• Pandemic response\n• Business adaptation\n• Remote operations\n• Strategic pivots",
            2019: "• Revenue growth\n• Product launches\n• Market expansion\n• Operational efficiency",
            2018: "• Earnings performance\n• Product development\n• Strategic investments\n• Market positioning"
        }
        return generic_events.get(year, "• Quarterly earnings reports\n• Product developments\n• Business initiatives\n• Operational updates")

def get_company_info_from_api(ticker):
    """Get company information from yfinance API with Canadian exchange support"""
    try:
        import yfinance as yf

        # Try normalized ticker first
        normalized_ticker = normalize_canadian_ticker(ticker)
        stock = yf.Ticker(normalized_ticker)
        info = stock.info

        if info and 'longName' in info:
            return {
                'name': info.get('longName', f'{ticker} Corporation'),
                'industry': info.get('industry', 'Unknown Industry'),
                'sector': info.get('sector', 'Unknown Sector'),
                'description': info.get('longBusinessSummary', f'{ticker} is a publicly traded company.'),
                'market_cap': f"${info.get('marketCap', 0) / 1e12:.1f}T" if info.get('marketCap', 0) > 1e12 else f"${info.get('marketCap', 0) / 1e9:.1f}B" if info.get('marketCap', 0) > 1e9 else 'N/A',
                'employees': f"{info.get('fullTimeEmployees', 0):,}" if info.get('fullTimeEmployees') else 'N/A',
                'founded': 'N/A',  # Not available in yfinance
                'headquarters': f"{info.get('city', '')}, {info.get('state', '')}" if info.get('city') else 'N/A',
                'key_products': info.get('longBusinessSummary', 'Various products and services')[:100] + '...' if info.get('longBusinessSummary') else 'Various products and services',
                'analyst_predictions': {
                    'price_target': f"${info.get('targetMeanPrice', 0):.2f}" if info.get('targetMeanPrice') else 'Not available',
                    'rating': info.get('recommendationKey', 'Not available').title(),
                    'growth_outlook': f"Revenue growth expected based on analyst estimates",
                    'key_catalysts': 'Business developments and market expansion',
                    'risks': 'Market volatility and industry-specific risks'
                }
            }

        # If normalized ticker failed, try variations
        if normalized_ticker == ticker:
            variations = try_canadian_variations(ticker)
            for variation in variations:
                if variation != ticker:
                    try:
                        stock = yf.Ticker(variation)
                        info = stock.info
                        if info and 'longName' in info:
                            return {
                                'name': info.get('longName', f'{ticker} Corporation'),
                                'industry': info.get('industry', 'Unknown Industry'),
                                'sector': info.get('sector', 'Unknown Sector'),
                                'description': info.get('longBusinessSummary', f'{ticker} is a publicly traded company.'),
                                'market_cap': f"${info.get('marketCap', 0) / 1e12:.1f}T" if info.get('marketCap', 0) > 1e12 else f"${info.get('marketCap', 0) / 1e9:.1f}B" if info.get('marketCap', 0) > 1e9 else 'N/A',
                                'employees': f"{info.get('fullTimeEmployees', 0):,}" if info.get('fullTimeEmployees') else 'N/A',
                                'founded': 'N/A',
                                'headquarters': f"{info.get('city', '')}, {info.get('state', '')}" if info.get('city') else 'N/A',
                                'key_products': info.get('longBusinessSummary', 'Various products and services')[:100] + '...' if info.get('longBusinessSummary') else 'Various products and services',
                                'analyst_predictions': {
                                    'price_target': f"${info.get('targetMeanPrice', 0):.2f}" if info.get('targetMeanPrice') else 'Not available',
                                    'rating': info.get('recommendationKey', 'Not available').title(),
                                    'growth_outlook': f"Revenue growth expected based on analyst estimates",
                                    'key_catalysts': 'Business developments and market expansion',
                                    'risks': 'Market volatility and industry-specific risks'
                                }
                            }
                    except:
                        continue

    except Exception as e:
        pass

    return None

def get_company_info(ticker):
    """Get comprehensive company information including analyst predictions"""

    # First try to get real data from API
    api_info = get_company_info_from_api(ticker)
    if api_info:
        return api_info


    # Fallback to static data for major tickers
    company_data = {
        'AAPL': {
            'name': 'Apple Inc.',
            'industry': 'Consumer Electronics & Technology',
            'sector': 'Technology',
            'description': 'Apple designs, manufactures, and markets smartphones, personal computers, tablets, wearables, and accessories. The company also sells various related services including digital content and cloud services. Apple is known for its iPhone, Mac, iPad, Apple Watch, and AirPods products, along with services like the App Store, Apple Music, and iCloud.',
            'market_cap': '$3.0T',
            'employees': '161,000',
            'founded': '1976',
            'headquarters': 'Cupertino, California',
            'key_products': 'iPhone (52% of revenue), Services (22%), Mac (10%), iPad (8%), Wearables (8%)',
            'analyst_predictions': {
                'price_target': '$220 (avg of 45 analysts)',
                'rating': 'Buy (28 Buy, 15 Hold, 2 Sell)',
                'growth_outlook': 'iPhone 16 cycle expected to drive 5-7% revenue growth in FY2024',
                'key_catalysts': 'Apple Intelligence rollout, Vision Pro expansion, Services growth',
                'risks': 'China market volatility, regulatory pressures, hardware saturation'
            }
        },
        'MSFT': {
            'name': 'Microsoft Corporation',
            'industry': 'Software & Cloud Computing',
            'sector': 'Technology',
            'description': 'Microsoft develops, licenses, and supports software, services, devices, and solutions worldwide. The company operates through three segments: Productivity and Business Processes (Office 365, Teams), Intelligent Cloud (Azure, Windows Server), and More Personal Computing (Windows, Xbox, Surface).',
            'market_cap': '$2.8T',
            'employees': '221,000',
            'founded': '1975',
            'headquarters': 'Redmond, Washington',
            'key_products': 'Azure (40% growth), Office 365 (440M seats), Windows, Xbox, Surface',
            'analyst_predictions': {
                'price_target': '$450 (avg of 42 analysts)',
                'rating': 'Strong Buy (35 Buy, 7 Hold, 0 Sell)',
                'growth_outlook': 'AI-driven growth expected to accelerate cloud revenue 25-30%',
                'key_catalysts': 'Copilot monetization, Azure AI services, OpenAI partnership',
                'risks': 'Cloud competition, AI investment costs, regulatory scrutiny'
            }
        },
        'GOOGL': {
            'name': 'Alphabet Inc. (Google)',
            'industry': 'Internet & Digital Advertising',
            'sector': 'Communication Services',
            'description': 'Alphabet is a holding company that gives ambitious projects the resources, freedom, and focus to make their ideas happen. Google, Alphabet\'s largest subsidiary, is a global technology leader focused on improving the ways people connect with information through search, advertising, cloud computing, and AI.',
            'market_cap': '$2.1T',
            'employees': '182,000',
            'founded': '1998',
            'headquarters': 'Mountain View, California',
            'key_products': 'Search (57% of revenue), YouTube (11%), Cloud (11%), Other Bets (1%)',
            'analyst_predictions': {
                'price_target': '$175 (avg of 40 analysts)',
                'rating': 'Buy (30 Buy, 9 Hold, 1 Sell)',
                'growth_outlook': 'Search AI integration and cloud growth driving 8-12% revenue growth',
                'key_catalysts': 'Gemini AI rollout, YouTube Shorts monetization, Cloud expansion',
                'risks': 'Antitrust regulations, AI competition, search disruption'
            }
        },
        'TSLA': {
            'name': 'Tesla, Inc.',
            'industry': 'Electric Vehicles & Clean Energy',
            'sector': 'Consumer Discretionary',
            'description': 'Tesla designs, develops, manufactures, and sells electric vehicles, energy generation and storage systems. The company operates through two segments: automotive (electric vehicles, regulatory credits) and energy generation and storage (solar panels, energy storage systems).',
            'market_cap': '$800B',
            'employees': '140,000',
            'founded': '2003',
            'headquarters': 'Austin, Texas',
            'key_products': 'Model 3/Y (85% of deliveries), Cybertruck, Energy Storage, FSD',
            'analyst_predictions': {
                'price_target': '$275 (avg of 35 analysts)',
                'rating': 'Hold (15 Buy, 15 Hold, 5 Sell)',
                'growth_outlook': 'Delivery growth of 15-25% expected with Cybertruck ramp',
                'key_catalysts': 'Cybertruck production, FSD breakthrough, energy business growth',
                'risks': 'EV competition, production challenges, regulatory changes'
            }
        },
        'AMZN': {
            'name': 'Amazon.com, Inc.',
            'industry': 'E-commerce & Cloud Computing',
            'sector': 'Consumer Discretionary',
            'description': 'Amazon is a multinational technology company focusing on e-commerce, cloud computing, digital streaming, and artificial intelligence. It operates through three segments: North America, International, and Amazon Web Services (AWS). The company is known for its disruption of well-established industries.',
            'market_cap': '$1.5T',
            'employees': '1,541,000',
            'founded': '1994',
            'headquarters': 'Seattle, Washington',
            'key_products': 'AWS (70% of operating income), Prime (200M+ members), Advertising, Logistics',
            'analyst_predictions': {
                'price_target': '$200 (avg of 38 analysts)',
                'rating': 'Buy (28 Buy, 9 Hold, 1 Sell)',
                'growth_outlook': 'AWS reacceleration and advertising growth driving 10-15% revenue growth',
                'key_catalysts': 'AWS AI services, Prime Video ads, logistics optimization',
                'risks': 'E-commerce saturation, cloud competition, regulatory pressures'
            }
        },
        'SHOP': {
            'name': 'Shopify Inc.',
            'industry': 'E-commerce Software',
            'sector': 'Technology',
            'description': 'Shopify provides a commerce platform and services in Canada, the United States, the United Kingdom, Australia, Latin America, and internationally. The company offers Shopify Plus, a commerce platform for enterprise brands; and Shopify Point of Sale, a cloud-based point-of-sale solution.',
            'market_cap': '$75B',
            'employees': '12,000',
            'founded': '2006',
            'headquarters': 'Ottawa, Ontario, Canada',
            'key_products': 'Shopify Platform, Shopify Plus, Shopify POS, Shopify Payments',
            'analyst_predictions': {
                'price_target': '$85 (avg of 25 analysts)',
                'rating': 'Buy (18 Buy, 6 Hold, 1 Sell)',
                'growth_outlook': 'E-commerce growth and international expansion driving 20-25% revenue growth',
                'key_catalysts': 'AI-powered features, B2B expansion, international markets',
                'risks': 'E-commerce competition, economic slowdown, merchant churn'
            }
        },
        'RY': {
            'name': 'Royal Bank of Canada',
            'industry': 'Banking & Financial Services',
            'sector': 'Financial Services',
            'description': 'Royal Bank of Canada operates as a diversified financial service company worldwide. The company provides personal and commercial banking, wealth management services, insurance, investor services, and capital markets products and services.',
            'market_cap': '$180B',
            'employees': '89,000',
            'founded': '1869',
            'headquarters': 'Toronto, Ontario, Canada',
            'key_products': 'Personal Banking, Commercial Banking, Wealth Management, Capital Markets',
            'analyst_predictions': {
                'price_target': '$145 (avg of 20 analysts)',
                'rating': 'Buy (15 Buy, 4 Hold, 1 Sell)',
                'growth_outlook': 'Steady dividend growth and market expansion driving 8-12% returns',
                'key_catalysts': 'Interest rate environment, US expansion, digital transformation',
                'risks': 'Credit losses, regulatory changes, economic downturn'
            }
        },
        'CNR': {
            'name': 'Canadian National Railway Company',
            'industry': 'Transportation & Logistics',
            'sector': 'Industrials',
            'description': 'Canadian National Railway Company, together with its subsidiaries, engages in the rail and related transportation business. The company transports cargo serving exporters, importers, retailers, farmers, and manufacturers.',
            'market_cap': '$85B',
            'employees': '24,000',
            'founded': '1919',
            'headquarters': 'Montreal, Quebec, Canada',
            'key_products': 'Rail Transportation, Intermodal Services, Automotive Transport, Forest Products',
            'analyst_predictions': {
                'price_target': '$165 (avg of 18 analysts)',
                'rating': 'Buy (12 Buy, 5 Hold, 1 Sell)',
                'growth_outlook': 'North American trade growth and efficiency improvements driving 10-15% growth',
                'key_catalysts': 'Trade volume recovery, operational efficiency, infrastructure investments',
                'risks': 'Economic slowdown, regulatory changes, weather disruptions'
            }
        }
    }

    # Return company data or generic info for unknown tickers
    if ticker.upper() in company_data:
        return company_data[ticker.upper()]
    else:
        return {
            'name': f'{ticker.upper()} Corporation',
            'industry': 'Various Industries',
            'sector': 'Mixed Sectors',
            'description': f'{ticker.upper()} is a publicly traded company. Detailed company information is not available in our database.',
            'market_cap': 'N/A',
            'employees': 'N/A',
            'founded': 'N/A',
            'headquarters': 'N/A',
            'key_products': 'Various products and services',
            'analyst_predictions': {
                'price_target': 'Not available',
                'rating': 'Not available',
                'growth_outlook': 'Consult financial advisors for investment guidance',
                'key_catalysts': 'Company-specific developments',
                'risks': 'Market volatility and company-specific risks'
            }
        }

def get_company_info_online(ticker):
    """Get enhanced company information from multiple online sources"""
    try:
        # First try the existing yfinance API function
        company_info = get_company_info_from_api(ticker)

        # If we got good data from yfinance, return it
        if (company_info and
            company_info.get('name') != f'{ticker} Corporation' and
            company_info.get('sector') != 'Unknown Sector'):
            return company_info

        # If yfinance didn't provide good data, try web scraping from MarketWatch
        return get_company_info_from_marketwatch(ticker)

    except Exception as e:
        return get_company_info_from_marketwatch(ticker)

def get_company_info_from_marketwatch(ticker):
    """Scrape company information from MarketWatch"""
    try:
        # Clean ticker for URL
        clean_ticker = ticker.replace('.TO', '').replace('.V', '').replace('.CN', '').replace('.NE', '')
        url = f"https://www.marketwatch.com/investing/stock/{clean_ticker}"

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')

            # Try to extract company name
            company_name = f'{ticker} Corporation'  # Default fallback

            # Look for company name in various locations
            name_selectors = [
                'h1.company__name',
                '.company__name',
                'h1[data-module="CompanyName"]',
                '.symbol-header h1',
                'h1.symbol__name',
                '.company-header h1'
            ]

            for selector in name_selectors:
                name_element = soup.select_one(selector)
                if name_element:
                    company_name = name_element.get_text().strip()
                    break

            # Try to extract sector information
            sector = 'Unknown Sector'
            sector_selectors = [
                '.company__sector',
                '[data-module="CompanySector"]',
                '.profile__sector',
                '.company-profile .sector',
                '.overview__sector'
            ]

            for selector in sector_selectors:
                sector_element = soup.select_one(selector)
                if sector_element:
                    sector = sector_element.get_text().strip()
                    break

            return {
                'name': company_name,
                'industry': 'Unknown Industry',
                'sector': sector,
                'description': f'{company_name} is a publicly traded company in the {sector} sector.',
                'market_cap': 'N/A',
                'employees': 'N/A',
                'founded': 'N/A',
                'headquarters': 'N/A',
                'key_products': 'Various products and services',
                'analyst_predictions': {
                    'price_target': 'Not available',
                    'rating': 'Not available',
                    'growth_outlook': 'Not available',
                    'key_catalysts': 'Not available',
                    'risks': 'Market volatility and industry-specific risks'
                }
            }

    except Exception as e:
        pass

    # Final fallback - return basic info
    return {
        'name': f'{ticker} Corporation',
        'industry': 'Unknown Industry',
        'sector': 'Unknown Sector',
        'description': f'{ticker} is a publicly traded company.',
        'market_cap': 'N/A',
        'employees': 'N/A',
        'founded': 'N/A',
        'headquarters': 'N/A',
        'key_products': 'Various products and services',
        'analyst_predictions': {
            'price_target': 'Not available',
            'rating': 'Not available',
            'growth_outlook': 'Not available',
            'key_catalysts': 'Not available',
            'risks': 'Market volatility and industry-specific risks'
        }
    }

def create_multi_ticker_price_chart(tickers, years_data_dict, num_years):
    """Create price history chart for multiple tickers"""
    fig = go.Figure()

    # Generate distinct colors for each ticker
    colors = generate_distinct_colors(len(tickers))

    # Get today's day of year for vertical line
    today = datetime.now()
    today_day_of_year = today.timetuple().tm_yday

    for i, ticker in enumerate(tickers):
        years_data = years_data_dict.get(ticker, {})
        if not years_data:
            continue

        # Combine all years data for this ticker
        all_dates = []
        all_prices = []

        # Sort years and combine data
        sorted_years = sorted(years_data.keys())
        for year in sorted_years[-num_years:]:  # Last N years
            data = years_data[year]
            if data is not None and not data.empty:
                all_dates.extend(data.index)
                all_prices.extend(data['Close'])

        if all_dates and all_prices:
            # Create combined dataframe and sort by date
            combined_df = pd.DataFrame({
                'Date': all_dates,
                'Price': all_prices
            }).sort_values('Date')

            # Normalize prices to start at 100
            start_price = combined_df['Price'].iloc[0]
            normalized_prices = (combined_df['Price'] / start_price) * 100

            fig.add_trace(go.Scatter(
                x=combined_df['Date'],
                y=normalized_prices,
                mode='lines',
                name=ticker,
                line=dict(color=colors[i], width=3),
                hovertemplate=f'<b>{ticker}</b><br>' +
                             'Date: %{x}<br>' +
                             'Normalized Price: %{y:.1f}<br>' +
                             '<extra></extra>'
            ))

    # Add horizontal line at 100 (starting point)
    fig.add_hline(y=100, line_dash="dash", line_color="gray", opacity=0.5)

    fig.update_layout(
        title=f'Multi-Ticker Price Comparison - Last {num_years} Years (Normalized)',
        xaxis_title='Date',
        yaxis_title='Normalized Price (Start = 100)',
        hovermode='closest',
        legend=dict(
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.02
        ),
        height=600
    )

    return fig

def create_multi_ticker_rsi_chart(tickers, years_data_dict, show_only_average=False):
    """Create seasonal RSI chart for multiple tickers"""
    fig = go.Figure()

    # Generate distinct colors for each ticker
    colors = generate_distinct_colors(len(tickers))

    for ticker_idx, ticker in enumerate(tickers):
        years_data = years_data_dict.get(ticker, {})
        if not years_data:
            continue

        # Sort years (oldest to newest)
        sorted_years = sorted(years_data.keys())

        # Calculate RSI for each year and collect monthly averages
        all_month_rsi = {}  # month -> list of RSI values

        for year in sorted_years:
            data = years_data[year]
            if data is not None and not data.empty:
                # Calculate RSI for this year
                rsi_values = calculate_rsi(data['Close'])

                # Add RSI column to data
                data_with_rsi = data.copy()
                data_with_rsi['RSI'] = rsi_values

                # Collect monthly RSI data
                for idx, row in data_with_rsi.iterrows():
                    month = idx.month
                    rsi_val = row['RSI']
                    if month not in all_month_rsi:
                        all_month_rsi[month] = []
                    all_month_rsi[month].append(rsi_val)

        if not all_month_rsi:
            continue

        # Calculate average RSI for each month
        avg_months = sorted(all_month_rsi.keys())
        avg_rsi = [np.mean(all_month_rsi[month]) for month in avg_months]

        # Convert months to day-of-year for plotting
        month_to_day = {1: 15, 2: 45, 3: 74, 4: 105, 5: 135, 6: 166,
                        7: 196, 8: 227, 9: 258, 10: 288, 11: 319, 12: 349}
        avg_days = [month_to_day[month] for month in avg_months]

        # Smooth the average RSI line
        if len(avg_days) > 3:
            from scipy.interpolate import interp1d
            f_avg = interp1d(avg_days, avg_rsi, kind='cubic', fill_value='extrapolate')
            smooth_days = np.linspace(1, 365, 365)
            smooth_rsi = f_avg(smooth_days)
        else:
            smooth_days = avg_days
            smooth_rsi = avg_rsi

        # Add average RSI line for this ticker
        fig.add_trace(go.Scatter(
            x=smooth_days,
            y=smooth_rsi,
            mode='lines',
            name=f'{ticker} RSI',
            line=dict(color=colors[ticker_idx], width=3),
            hovertemplate=f'<b>{ticker} RSI</b><br>' +
                         'Day of Year: %{x:.0f}<br>' +
                         'RSI: %{y:.1f}<br>' +
                         '<extra></extra>'
        ))

    # Add RSI reference lines
    fig.add_hline(y=70, line_dash="dash", line_color="red", opacity=0.7,
                  annotation_text="Overbought (70)", annotation_position="bottom right")
    fig.add_hline(y=30, line_dash="dash", line_color="green", opacity=0.7,
                  annotation_text="Oversold (30)", annotation_position="top right")
    fig.add_hline(y=50, line_dash="dot", line_color="gray", opacity=0.5,
                  annotation_text="Neutral (50)", annotation_position="top left")

    fig.update_layout(
        title=f'Multi-Ticker Seasonal RSI Comparison',
        xaxis_title='Day of Year',
        yaxis_title='RSI (Relative Strength Index)',
        yaxis=dict(range=[0, 100]),
        hovermode='closest',
        legend=dict(
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.02
        ),
        height=600
    )

    # Add month markers
    month_days = [1, 32, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335]
    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

    fig.update_layout(
        xaxis=dict(
            tickmode='array',
            tickvals=month_days,
            ticktext=month_names
        )
    )

    return fig

def cache_management_page():
    """Cache management page"""
    st.markdown("## 📁 Cache Management")
    st.markdown("Manage your saved stock analyses and cached data.")

    # Get all cached entries
    cached_entries = get_all_cached_entries()

    if not cached_entries:
        st.info("No cached analyses found. Run some analyses first to see them here.")
        return

    st.success(f"Found {len(cached_entries)} cached analyses")

    # Display cached entries in a more detailed format
    for i, entry in enumerate(cached_entries):
        with st.container():
            col1, col2, col3 = st.columns([3, 1, 1])

            with col1:
                # Entry details
                if entry.get('type') == 'multi':
                    ticker_count = len(entry['tickers'])
                    st.markdown(f"**🔄 Multi-Ticker Analysis**")
                    st.markdown(f"**Tickers:** {', '.join(entry['tickers'])}")
                    st.markdown(f"**Count:** {ticker_count} stocks")
                else:
                    st.markdown(f"**📊 Single Stock Analysis**")
                    st.markdown(f"**Ticker:** {entry['ticker']}")

                    # Display company info if available
                    if 'company_info' in entry:
                        company_info = entry['company_info']
                        st.markdown(f"**Company:** {company_info.get('name', entry['ticker'])}")
                        st.markdown(f"**Sector:** {company_info.get('sector', 'N/A')}")

                st.markdown(f"**Years:** {entry['num_years']}")
                st.markdown(f"**Cached:** {entry['timestamp'].strftime('%Y-%m-%d %H:%M')}")

            with col2:
                # Load button
                if entry.get('type') == 'multi':
                    button_text = f"Load Multi-Ticker"
                    help_text = f"Load cached multi-ticker analysis for {len(entry['tickers'])} stocks"
                else:
                    button_text = f"Load Analysis"
                    help_text = f"Load cached analysis for {entry['ticker']}"

                if st.button(
                    button_text,
                    key=f"load_cache_{i}_{entry['cache_key'][:8]}",
                    help=help_text
                ):
                    if entry.get('type') == 'multi':
                        # Load multi-ticker cache
                        cached_data = load_multi_ticker_from_cache(
                            entry['tickers'],
                            entry['num_years']
                        )

                        if cached_data:
                            # Set session state for multi-ticker cache
                            st.session_state.load_cached_multi_data = cached_data
                            st.session_state.cached_tickers = entry['tickers']
                            st.session_state.cached_num_years = entry['num_years']
                            st.session_state.trigger_multi_cache_analysis = True
                            st.session_state.page = "Analysis"  # Switch to analysis page
                            st.rerun()
                    else:
                        # Load single ticker cache
                        cached_data = load_from_cache(
                            entry['ticker'],
                            entry['num_years']
                        )

                        if cached_data:
                            # Set session state to load cached data
                            st.session_state.load_cached_data = cached_data
                            st.session_state.cached_ticker = entry['ticker']
                            st.session_state.cached_num_years = entry['num_years']
                            st.session_state.trigger_cache_analysis = True
                            st.session_state.page = "Analysis"  # Switch to analysis page
                            st.rerun()

            with col3:
                # Delete button
                if st.button(
                    "🗑️ Delete",
                    key=f"delete_cache_{i}_{entry['cache_key'][:8]}",
                    help=f"Delete this cached analysis"
                ):
                    if delete_single_cache(entry['cache_key']):
                        st.success("Cache entry deleted!")
                        st.rerun()
                    else:
                        st.error("Failed to delete cache entry")

            st.divider()

    # Clear all cache button
    st.markdown("---")
    col1, col2 = st.columns([1, 1])

    with col1:
        if st.button("🗑️ Clear All Cache", help="Remove all cached analyses"):
            cleared_count = clear_cache()
            st.success(f"Cleared {cleared_count} cached analyses")
            st.rerun()

    with col2:
        st.markdown(f"**Total cache entries:** {len(cached_entries)}")

def main():
    st.set_page_config(
        page_title="Stock Analysis Suite",
        page_icon="📈",
        layout="wide"
    )

    # Page navigation
    if 'page' not in st.session_state:
        st.session_state.page = "Analysis"

    # Navigation tabs
    tab1, tab2 = st.tabs(["📊 Analysis", "📁 Cache Management"])

    with tab1:
        st.session_state.page = "Analysis"
        st.title("📈 Seasonal Stock Analysis")
        st.markdown("Analyze seasonal stock price movements for single stocks or compare multiple tickers")
        seasonal_analyzer_page()

    with tab2:
        st.session_state.page = "Cache"
        cache_management_page()

def seasonal_analyzer_page():
    """Seasonal stock analysis page with multi-ticker support"""

    # Sidebar inputs
    st.sidebar.subheader("Analysis Settings")
    
    # Handle ticker input persistence - remember last used ticker(s)
    if 'reload_from_cache' in st.session_state and st.session_state.reload_from_cache:
        default_ticker = st.session_state.get('selected_ticker', '')
        st.session_state.reload_from_cache = False
    elif 'cached_ticker' in st.session_state:
        # Single ticker from cache
        default_ticker = st.session_state.cached_ticker
        # Remember this as the last used ticker
        st.session_state.last_used_ticker = st.session_state.cached_ticker
    elif 'cached_tickers' in st.session_state:
        # Multi-ticker cache - join with commas
        default_ticker = ', '.join(st.session_state.cached_tickers)
        # Remember this as the last used ticker(s)
        st.session_state.last_used_ticker = ', '.join(st.session_state.cached_tickers)
    else:
        # Use last used ticker if available, otherwise empty
        default_ticker = st.session_state.get('last_used_ticker', '')

    # Get default values from cache if available or set default
    default_num_years = st.session_state.get('cached_num_years', 5)

    # Initialize num_years if not set
    if 'num_years' not in st.session_state:
        st.session_state.num_years = default_num_years

    # Ticker input - supports single or multiple tickers
    ticker_input = st.sidebar.text_input(
        "Stock Ticker Symbol(s)",
        value=default_ticker,
        placeholder="Enter ticker(s) e.g., AAPL or AAPL, MSFT, GOOGL",
        help="Enter one or more stock ticker symbols separated by commas (e.g., AAPL or AAPL, MSFT, GOOGL for comparison)"
    ).upper()

    # Parse ticker input - detect single vs multiple tickers
    if ticker_input.strip():  # Only process if there's actual input
        # Remember this as the last used ticker for future sessions
        st.session_state.last_used_ticker = ticker_input

        if ',' in ticker_input:
            # Multiple tickers - split and clean
            tickers = [t.strip() for t in ticker_input.split(',') if t.strip()]

            # Limit to 10 tickers for performance
            if len(tickers) > 10:
                st.sidebar.warning("⚠️ Maximum 10 tickers allowed for performance reasons")
                tickers = tickers[:10]

            is_multi_ticker = True
            ticker = tickers[0] if tickers else ""  # Use first ticker for cache/form logic

            # Show ticker count
            if tickers:
                st.sidebar.caption(f"📊 Analyzing {len(tickers)} tickers: {', '.join(tickers)}")
        else:
            # Single ticker
            tickers = [ticker_input.strip()]
            is_multi_ticker = False
            ticker = tickers[0]
    else:
        # Empty input - no analysis possible
        tickers = []
        is_multi_ticker = False
        ticker = ""

    # Year selection buttons
    st.sidebar.subheader("Number of Years to Compare")

    # Create buttons in a grid layout
    col1, col2, col3 = st.sidebar.columns(3)
    col4, col5, col6 = st.sidebar.columns(3)

    # First row of buttons
    with col1:
        if st.button("2 Years", key="years_2",
                    type="primary" if st.session_state.num_years == 2 else "secondary"):
            st.session_state.num_years = 2
            st.rerun()

    with col2:
        if st.button("5 Years", key="years_5",
                    type="primary" if st.session_state.num_years == 5 else "secondary"):
            st.session_state.num_years = 5
            st.rerun()

    with col3:
        if st.button("10 Years", key="years_10",
                    type="primary" if st.session_state.num_years == 10 else "secondary"):
            st.session_state.num_years = 10
            st.rerun()

    # Second row of buttons
    with col4:
        if st.button("15 Years", key="years_15",
                    type="primary" if st.session_state.num_years == 15 else "secondary"):
            st.session_state.num_years = 15
            st.rerun()

    with col5:
        if st.button("20 Years", key="years_20",
                    type="primary" if st.session_state.num_years == 20 else "secondary"):
            st.session_state.num_years = 20
            st.rerun()

    with col6:
        if st.button("25 Years", key="years_25",
                    type="primary" if st.session_state.num_years == 25 else "secondary"):
            st.session_state.num_years = 25
            st.rerun()

    # Display current selection
    st.sidebar.caption(f"Selected: {st.session_state.num_years} years")

    # Set num_years for the analysis
    num_years = st.session_state.num_years
    # Fixed date range: Always January 1 to December 31
    start_month = 1
    start_day = 1
    end_month = 12
    end_day = 31
    
    # Chart options
    st.sidebar.subheader("Chart Options")
    use_log_scale = st.sidebar.checkbox(
        "Use Logarithmic Scale for Price Chart",
        value=False,
        help="Logarithmic scale is useful for comparing percentage changes across different price levels"
    )

    show_only_average = st.sidebar.checkbox(
        "Show Only Average Line",
        value=False,
        help="Hide individual years and show only the smooth average trend"
    )



    # # Cache management section
    # st.sidebar.subheader("📁 Cached Analyses")

    # # Get all cached entries
    # cached_entries = get_all_cached_entries()

    # if cached_entries:
    #     st.sidebar.caption(f"Found {len(cached_entries)} cached analyses")

    #     # Display cached entries as clickable buttons
    #     for i, entry in enumerate(cached_entries[:10]):  # Show max 10 entries
    #         # Create display text
    #         date_range = f"{entry['start_month']}/{entry['start_day']} - {entry['end_month']}/{entry['end_day']}"
    #         button_text = f"{entry['ticker']} ({entry['num_years']}y) {date_range}"

    #         # Create unique key for button
    #         button_key = f"cache_load_{i}_{entry['cache_key'][:8]}"

    #         if st.sidebar.button(
    #             button_text,
    #             key=button_key,
    #             help=f"Load cached analysis for {entry['ticker']} with {entry['num_years']} years"
    #         ):
    #             # Load from cache and set session state
    #             cached_data = load_from_cache(
    #                 entry['ticker'],
    #                 entry['num_years'],
    #                 entry['start_month'],
    #                 entry['start_day'],
    #                 entry['end_month'],
    #                 entry['end_day']
    #             )

    #             if cached_data:
    #                 # Set session state to load cached data
    #                 st.session_state.load_cached_data = cached_data
    #                 st.session_state.cached_ticker = entry['ticker']
    #                 st.session_state.cached_num_years = entry['num_years']
    #                 st.session_state.cached_start_month = entry['start_month']
    #                 st.session_state.cached_start_day = entry['start_day']
    #                 st.session_state.cached_end_month = entry['end_month']
    #                 st.session_state.cached_end_day = entry['end_day']
    #                 st.rerun()

    #     if len(cached_entries) > 10:
    #         st.sidebar.caption(f"... and {len(cached_entries) - 10} more")

    #     # Clear cache button
    #     if st.sidebar.button("🗑️ Clear All Cache", help="Remove all cached analyses"):
    #         cleared_count = clear_cache()
    #         st.sidebar.success(f"Cleared {cleared_count} cached analyses")
    #         st.rerun()
    # else:
    #     st.sidebar.caption("No cached analyses found")

    # Analysis button - always required
    analyze_button = st.sidebar.button("Analyze Seasonal Patterns", type="primary")

    # Check if we should load cached data or trigger cache analysis
    load_cached = False
    load_multi_cached = False
    trigger_cache_analysis = st.session_state.get('trigger_cache_analysis', False)
    trigger_multi_cache_analysis = st.session_state.get('trigger_multi_cache_analysis', False)

    if 'load_cached_data' in st.session_state:
        cached_data = st.session_state.load_cached_data

        # Update form values to match cached data
        ticker = st.session_state.cached_ticker
        num_years = st.session_state.cached_num_years

        load_cached = True

        # Clear the session state
        del st.session_state.load_cached_data
        del st.session_state.cached_ticker
        del st.session_state.cached_num_years

    # Handle multi-ticker cache loading
    if 'load_cached_multi_data' in st.session_state:
        cached_multi_data = st.session_state.load_cached_multi_data
        load_multi_cached = True

        # Clear the session state
        del st.session_state.load_cached_multi_data
        del st.session_state.cached_tickers
        del st.session_state.cached_num_years

    # Clear trigger flags if they exist
    if 'trigger_cache_analysis' in st.session_state:
        del st.session_state.trigger_cache_analysis
    if 'trigger_multi_cache_analysis' in st.session_state:
        del st.session_state.trigger_multi_cache_analysis

    if analyze_button or load_cached or trigger_cache_analysis or load_multi_cached or trigger_multi_cache_analysis:
        if not ticker or not tickers:
            st.error("Please enter valid ticker symbol(s)")
            st.info("💡 **Examples:** AAPL, MSFT, GOOGL (single or multiple tickers separated by commas)")
            return

        # Display analysis type
        if is_multi_ticker:
            st.info(f"🔄 Multi-Ticker Analysis: {', '.join(tickers)} ({len(tickers)} stocks)")
        else:
            st.info(f"📊 Single Stock Analysis: {ticker}")

        # Check if we're loading from cache or triggered by cache button
        if load_cached or trigger_cache_analysis:
            # Use cached data with same loading experience as fresh analysis
            years_data = cached_data['years_data']

            # Show loading spinner for consistent UX (same as fresh data loading)
            with st.spinner(f"📁 Loading cached analysis for {ticker}..."):
                import time
                time.sleep(0.8)  # Brief pause for consistent loading experience

            st.success(f"✅ Loaded cached analysis for {ticker} ({len(years_data)} years)")
        elif load_multi_cached or trigger_multi_cache_analysis:
            # Use cached multi-ticker data with same loading display
            all_years_data = cached_multi_data['all_years_data']
            successful_tickers = list(all_years_data.keys())

            # Show loading spinner for consistent UX
            with st.spinner(f"📁 Loading cached multi-ticker analysis for {len(successful_tickers)} tickers..."):
                import time
                time.sleep(0.8)  # Brief pause for consistent loading experience

            st.success(f"✅ Loaded cached multi-ticker analysis ({len(successful_tickers)} tickers)")

            # Display multi-ticker charts directly from cache
            st.markdown("---")
            st.subheader("📈 Multi-Ticker Price Comparison")
            st.caption(f"💡 Normalized prices (all start at 100) • {len(successful_tickers)} tickers over {num_years} years")

            fig_multi_price = create_multi_ticker_price_chart(successful_tickers, all_years_data, num_years)
            if fig_multi_price:
                st.plotly_chart(fig_multi_price, use_container_width=True)

            st.subheader("📊 Multi-Ticker Seasonal RSI Comparison")
            st.caption(f"💡 RSI patterns • 70+ = Overbought, 30- = Oversold • Average trends across seasonal period")

            fig_multi_rsi = create_multi_ticker_rsi_chart(successful_tickers, all_years_data)
            if fig_multi_rsi:
                st.plotly_chart(fig_multi_rsi, use_container_width=True)

            # Summary table for cached multi-ticker
            st.subheader("📋 Multi-Ticker Summary")

            summary_data = []
            for ticker_name in successful_tickers:
                ticker_data = all_years_data[ticker_name]
                total_years = len(ticker_data)

                # Calculate average metrics across all years
                all_prices = []
                for year_data in ticker_data.values():
                    if year_data is not None and not year_data.empty:
                        all_prices.extend(year_data['Close'].tolist())

                if all_prices:
                    avg_price = np.mean(all_prices)
                    price_range = f"${min(all_prices):.2f} - ${max(all_prices):.2f}"
                    volatility = np.std(all_prices) / avg_price * 100
                else:
                    avg_price = 0
                    price_range = "N/A"
                    volatility = 0

                summary_data.append({
                    'Ticker': ticker_name,
                    'Years Loaded': total_years,
                    'Avg Price': f"${avg_price:.2f}",
                    'Price Range': price_range,
                    'Volatility (σ)': f"{volatility:.1f}%"
                })

            if summary_data:
                summary_df = pd.DataFrame(summary_data)
                st.dataframe(summary_df, use_container_width=True)

            return  # Exit early for cached multi-ticker analysis
        elif is_multi_ticker:
            # Multi-ticker analysis - fetch data for all tickers
            with st.spinner(f"🔄 Fetching data for {len(tickers)} tickers..."):
                all_years_data = {}
                failed_tickers = []

                # Progress tracking
                progress_bar = st.progress(0)
                status_text = st.empty()

                for i, current_ticker in enumerate(tickers):
                    status_text.text(f"Loading {current_ticker}... ({i+1}/{len(tickers)})")

                    # Fetch data for this ticker
                    ticker_years_data = {}
                    current_year = datetime.now().year

                    for j in range(num_years):
                        year = current_year - j

                        try:
                            start_date = datetime(year, start_month, start_day)
                            end_date = datetime(year, end_month, end_day)

                            # Handle year wrap-around
                            if end_date < start_date:
                                end_date = datetime(year + 1, end_month, end_day)

                        except ValueError:
                            # Handle invalid dates
                            start_date = datetime(year, start_month, min(start_day, 28))
                            end_date = datetime(year, end_month, min(end_day, 28))
                            if end_date < start_date:
                                end_date = datetime(year + 1, end_month, min(end_day, 28))

                        # Fetch data for this year
                        data = fetch_stock_data(current_ticker, start_date, end_date)

                        if data is not None and not data.empty:
                            normalized_data = normalize_to_day_of_year(data, year)
                            ticker_years_data[year] = normalized_data

                    if ticker_years_data:
                        all_years_data[current_ticker] = ticker_years_data
                    else:
                        failed_tickers.append(current_ticker)

                    progress_bar.progress((i + 1) / len(tickers))

                progress_bar.empty()
                status_text.empty()

                # Check results
                successful_tickers = list(all_years_data.keys())
                if not successful_tickers:
                    st.error(f"❌ No data found for any of the tickers: {', '.join(tickers)}")
                    return

                # Display results
                success_msg = f"✅ Successfully loaded data for {len(successful_tickers)} out of {len(tickers)} tickers"
                if failed_tickers:
                    success_msg += f" (failed: {', '.join(failed_tickers)})"
                st.success(success_msg)

                # Create multi-ticker charts
                st.markdown("---")
                st.subheader("📈 Multi-Ticker Price Comparison")
                st.caption(f"💡 Normalized prices (all start at 100) • {len(successful_tickers)} tickers over {num_years} years")

                fig_multi_price = create_multi_ticker_price_chart(successful_tickers, all_years_data, num_years)
                if fig_multi_price:
                    st.plotly_chart(fig_multi_price, use_container_width=True)

                st.subheader("📊 Multi-Ticker Seasonal RSI Comparison")
                st.caption(f"💡 RSI patterns • 70+ = Overbought, 30- = Oversold • Average trends across seasonal period")

                fig_multi_rsi = create_multi_ticker_rsi_chart(successful_tickers, all_years_data)
                if fig_multi_rsi:
                    st.plotly_chart(fig_multi_rsi, use_container_width=True)

                # Summary table for multi-ticker
                st.subheader("📋 Multi-Ticker Summary")

                summary_data = []
                for ticker_name in successful_tickers:
                    ticker_data = all_years_data[ticker_name]
                    total_years = len(ticker_data)

                    # Calculate average metrics across all years
                    all_prices = []
                    for year_data in ticker_data.values():
                        if year_data is not None and not year_data.empty:
                            all_prices.extend(year_data['Close'].tolist())

                    if all_prices:
                        avg_price = np.mean(all_prices)
                        price_range = f"${min(all_prices):.2f} - ${max(all_prices):.2f}"
                        volatility = np.std(all_prices) / avg_price * 100
                    else:
                        avg_price = 0
                        price_range = "N/A"
                        volatility = 0

                    summary_data.append({
                        'Ticker': ticker_name,
                        'Years Loaded': total_years,
                        'Avg Price': f"${avg_price:.2f}",
                        'Price Range': price_range,
                        'Volatility (σ)': f"{volatility:.1f}%"
                    })

                if summary_data:
                    summary_df = pd.DataFrame(summary_data)
                    st.dataframe(summary_df, use_container_width=True)

                # Save multi-ticker analysis to cache
                if successful_tickers:
                    save_success = save_multi_ticker_to_cache(
                        successful_tickers,
                        num_years,
                        all_years_data
                    )
                    if save_success:
                        st.success(f"💾 Multi-ticker analysis saved to cache for future use")

                return  # Exit early for multi-ticker analysis
        else:
            # Check if data exists in cache first
            cached_analysis = load_from_cache(ticker, num_years)

            if cached_analysis:
                # Use cached data
                years_data = cached_analysis['years_data']
                st.info(f"📁 Found in cache: {ticker} with {num_years} years of data")
            else:
                # Fetch fresh data
                with st.spinner(f"🌐 Fetching fresh data for {ticker}..."):
                    current_year = datetime.now().year
                    years_data = {}
                    failed_years = []

                    # Progress bar
                    progress_bar = st.progress(0)
                    status_text = st.empty()

                    for i in range(num_years):
                        year = current_year - i  # Include current year
                        status_text.text(f"Loading data for {year}...")

                        # Create date range for this year
                        try:
                            start_date = datetime(year, start_month, start_day)
                            end_date = datetime(year, end_month, end_day)

                            # Handle year wrap-around (e.g., Nov to Feb)
                            if end_date < start_date:
                                end_date = datetime(year + 1, end_month, end_day)

                        except ValueError:
                            # Handle invalid dates (e.g., Feb 31)
                            start_date = datetime(year, start_month, min(start_day, 28))
                            end_date = datetime(year, end_month, min(end_day, 28))
                            if end_date < start_date:
                                end_date = datetime(year + 1, end_month, min(end_day, 28))

                        # Fetch data for this year
                        data = fetch_stock_data(ticker, start_date, end_date)

                        if data is not None and not data.empty:
                            # Normalize to day of year
                            normalized_data = normalize_to_day_of_year(data, year)
                            years_data[year] = normalized_data
                        else:
                            failed_years.append(year)

                        progress_bar.progress((i + 1) / num_years)

                    progress_bar.empty()
                    status_text.empty()

                    # Save to cache if we successfully fetched data
                    if years_data:
                        # Get company info for caching (will be used later for display too)
                        try:
                            company_info = get_company_info_online(ticker)
                        except Exception as e:
                            st.warning(f"Could not fetch company info for caching: {str(e)}")
                            company_info = None

                        save_success = save_to_cache(ticker, num_years, years_data, company_info)
                        if save_success:
                            st.success(f"💾 Data saved to cache for future use")


            
            if not years_data:
                st.error(f"❌ No data found for ticker {ticker} in the specified date range")
                if failed_years:
                    st.warning(f"Failed to fetch data for years: {', '.join(map(str, failed_years))}")
                st.info("""
                **Possible solutions:**
                - Check if the ticker symbol is correct (e.g., AAPL, MSFT, GOOGL)
                - Try a different date range
                - Wait a moment and try again (Yahoo Finance sometimes has rate limits)
                - Try a more popular stock ticker
                """)
                return

            # Display results for fresh data only
            if not (load_cached or trigger_cache_analysis):
                success_msg = f"✅ Successfully loaded data for {len(years_data)} years"
                # if failed_years:
                #     success_msg += f" (failed: {', '.join(map(str, failed_years))})"
                st.success(success_msg)

        # Check if we have data to display (applies to both cached and fresh data)
        if not years_data:
            st.error(f"❌ No data available for analysis")
            return

        # Display company information (applies to both cached and fresh data)
        # Use online lookup for better company name and sector information
        # Always fetch company info for display (may have been fetched earlier for caching)
        try:
            company_info = get_company_info_online(ticker)
        except Exception as e:
            st.error(f"Could not fetch company information: {str(e)}")
            # Provide fallback company info
            company_info = {
                'name': f'{ticker} Corporation',
                'industry': 'Unknown Industry',
                'sector': 'Unknown Sector',
                'description': f'{ticker} is a publicly traded company.',
                'market_cap': 'N/A',
                'employees': 'N/A',
                'founded': 'N/A',
                'headquarters': 'N/A',
                'key_products': 'Various products and services',
                'analyst_predictions': {
                    'price_target': 'Not available',
                    'rating': 'Not available',
                    'growth_outlook': 'Not available',
                    'key_catalysts': 'Not available',
                    'risks': 'Market volatility and industry-specific risks'
                }
            }

        st.markdown("---")
        col1, col2 = st.columns([2, 1])

        with col1:
            # Create clickable company name with MarketWatch link
            marketwatch_url = f"https://www.marketwatch.com/investing/stock/{ticker.lower()}"
            st.subheader(f"🏢 [{company_info['name']}]({marketwatch_url})")
            st.markdown(f"**Industry:** {company_info['industry']} | **Sector:** {company_info['sector']}")
            st.markdown(f"**Description:** {company_info['description']}")

            # Key business metrics
            st.markdown(f"**Key Products/Services:** {company_info['key_products']}")

        with col2:
            st.markdown("**Company Facts:**")
            st.markdown(f"• **Market Cap:** {company_info['market_cap']}")
            st.markdown(f"• **Employees:** {company_info['employees']}")
            st.markdown(f"• **Founded:** {company_info['founded']}")
            st.markdown(f"• **HQ:** {company_info['headquarters']}")

        # Analyst predictions section
        st.markdown("### 📊 Analyst Outlook & Predictions")
        pred = company_info['analyst_predictions']

        col1, col2, col3 = st.columns(3)
        with col1:
            st.markdown(f"**Price Target:** {pred['price_target']}")
            st.markdown(f"**Rating:** {pred['rating']}")

        with col2:
            st.markdown(f"**Growth Outlook:** {pred['growth_outlook']}")

        with col3:
            st.markdown(f"**Key Catalysts:** {pred['key_catalysts']}")
            st.markdown(f"**Key Risks:** {pred['risks']}")

        st.markdown("---")

        # Create and display charts (applies to both cached and fresh data)
        st.subheader("📈 Price Comparison")
        caption_text = "💡 Showing only average trend" if show_only_average else "💡 Each year in distinct colors • Thick gray line shows smooth monthly average trend"
        st.caption(caption_text)
        fig_price = create_seasonal_price_chart(ticker, years_data, use_log_scale, show_only_average)
        st.plotly_chart(fig_price, use_container_width=True)

        st.subheader("📊 Normalized Seasonal Patterns")
        caption_text_norm = "💡 All prices start at 100 for easier comparison • Shows relative performance" if not show_only_average else "💡 Normalized average trend starting at 100"
        st.caption(caption_text_norm)
        fig_normalized = create_normalized_seasonal_chart(ticker, years_data, show_only_average)
        st.plotly_chart(fig_normalized, use_container_width=True)

        st.subheader("📊 Multi-Year Price History")
        st.caption(f"💡 Continuous price line for last {min(num_years, len(years_data))} years")
        fig_multi_year = create_multi_year_line_chart(ticker, years_data, num_years)
        st.plotly_chart(fig_multi_year, use_container_width=True)

        st.subheader("📈 Seasonal RSI Analysis")
        caption_text_rsi = "💡 RSI momentum patterns • 70+ = Overbought, 30- = Oversold" if not show_only_average else "💡 Average RSI trend with overbought/oversold levels"
        st.caption(caption_text_rsi)
        fig_rsi = create_rsi_chart(ticker, years_data, show_only_average)
        st.plotly_chart(fig_rsi, use_container_width=True)

        # Kalman Filter Analysis
        st.subheader("🔬 Kalman Filter Trend Analysis")
        st.caption("💡 Advanced statistical filtering to identify underlying price trends and reduce market noise")

        kalman_fig = create_kalman_filter_chart(ticker, years_data, show_confidence_bands=True)
        if kalman_fig:
            st.plotly_chart(kalman_fig, use_container_width=True)

            # Add explanation
            with st.expander("📖 Understanding Kalman Filter Analysis"):
                st.markdown("""
                **Kalman Filter** is a mathematical algorithm that uses statistical methods to estimate the true underlying trend of a stock price by filtering out market noise.

                **Chart Elements:**
                - **Actual Price** (dotted gray): Raw stock price data
                - **Kalman Smooth** (blue): Conservative trend estimate with minimal noise
                - **Kalman Responsive** (orange): More responsive trend that adapts quickly to changes
                - **95% Confidence Band** (light blue): Statistical confidence interval for the trend
                - **Trend Arrow**: Current trend direction with slope indicator

                **Investment Insights:**
                - **Smooth Trend**: Best for long-term trend identification
                - **Responsive Trend**: Better for shorter-term trend changes
                - **Confidence Bands**: Price movements outside bands may indicate significant events
                - **Trend Direction**: Current momentum and slope analysis

                **Use Cases:**
                - Identify true trend direction amid market volatility
                - Spot potential trend reversals
                - Filter out short-term noise for clearer analysis
                - Assess trend strength and consistency
                """)
        else:
            st.warning("Unable to generate Kalman filter analysis - insufficient data")

        # Recent year comparison chart
        current_year = datetime.now().year
        last_year = current_year - 1

        if last_year in years_data and current_year in years_data:
            st.subheader("🔍 Recent Year Focus")
            st.caption(f"💡 Direct comparison: {last_year} vs {current_year}")
            fig_recent = create_recent_comparison_chart(ticker, years_data)
            st.plotly_chart(fig_recent, use_container_width=True)
        elif current_year in years_data:
            st.subheader("🔍 Recent Year Focus")
            st.caption(f"💡 Only {current_year} data available for recent comparison")
            # Create chart with just current year
            recent_data = {current_year: years_data[current_year]}
            fig_recent = create_recent_comparison_chart(ticker, recent_data)
            st.plotly_chart(fig_recent, use_container_width=True)


if __name__ == "__main__":
    main()
