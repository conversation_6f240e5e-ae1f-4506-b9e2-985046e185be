#!/usr/bin/env python3
"""
Test script for Multi-Ticker Cache Functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import (
    save_multi_ticker_to_cache, 
    load_multi_ticker_from_cache, 
    get_all_cached_entries, 
    fetch_stock_data, 
    normalize_to_day_of_year,
    clear_cache
)
from datetime import datetime

def test_multi_ticker_cache_operations():
    """Test multi-ticker cache save and load operations"""
    print("Testing Multi-Ticker Cache Operations")
    print("=" * 50)
    
    # Test data
    test_tickers = ['AAPL', 'MSFT']
    test_years = 2
    test_start_month = 1
    test_start_day = 1
    test_end_month = 12
    test_end_day = 31
    
    print(f"Testing with tickers: {', '.join(test_tickers)}")
    print(f"Analysis period: {test_years} years")
    
    # Create test multi-ticker data structure
    all_years_data = {}
    current_year = datetime.now().year
    
    for ticker in test_tickers:
        print(f"\nCreating test data for {ticker}...")
        ticker_years_data = {}
        
        for i in range(test_years):
            year = current_year - i
            
            # Fetch real data for testing
            start_date = datetime(year, test_start_month, test_start_day)
            end_date = datetime(year, test_end_month, test_end_day)
            
            data = fetch_stock_data(ticker, start_date, end_date)
            
            if data is not None and not data.empty:
                normalized_data = normalize_to_day_of_year(data, year)
                ticker_years_data[year] = normalized_data
                print(f"  ✅ {year}: {len(data)} days loaded")
            else:
                print(f"  ❌ {year}: No data")
        
        if ticker_years_data:
            all_years_data[ticker] = ticker_years_data
            print(f"  Total: {len(ticker_years_data)} years for {ticker}")
    
    if not all_years_data:
        print("❌ No test data available")
        return False
    
    print(f"\nTesting multi-ticker cache save...")
    
    # Save to cache
    try:
        save_success = save_multi_ticker_to_cache(
            test_tickers,
            test_years,
            test_start_month,
            test_start_day,
            test_end_month,
            test_end_day,
            all_years_data
        )
        
        if save_success:
            print("✅ Multi-ticker cache save successful")
        else:
            print("❌ Multi-ticker cache save failed")
            return False
    except Exception as e:
        print(f"❌ Multi-ticker cache save error: {e}")
        return False
    
    # Test cache retrieval
    print(f"\nTesting multi-ticker cache load...")
    try:
        loaded_data = load_multi_ticker_from_cache(
            test_tickers,
            test_years,
            test_start_month,
            test_start_day,
            test_end_month,
            test_end_day
        )
        
        if loaded_data:
            print("✅ Multi-ticker cache load successful")
            print(f"  Cache type: {loaded_data.get('type', 'unknown')}")
            print(f"  Cached tickers: {loaded_data.get('tickers', [])}")
            print(f"  Cached years: {loaded_data.get('num_years', 0)}")
            print(f"  Data structure: {list(loaded_data.get('all_years_data', {}).keys())}")
            
            # Verify data integrity
            cached_all_years_data = loaded_data.get('all_years_data', {})
            for ticker in test_tickers:
                if ticker in cached_all_years_data:
                    ticker_data = cached_all_years_data[ticker]
                    print(f"    {ticker}: {len(ticker_data)} years cached")
                else:
                    print(f"    {ticker}: Missing from cache")
            
        else:
            print("❌ Multi-ticker cache load returned None")
            return False
    except Exception as e:
        print(f"❌ Multi-ticker cache load error: {e}")
        return False
    
    return True

def test_mixed_cache_entries():
    """Test that both single and multi-ticker cache entries are handled correctly"""
    print("\nTesting Mixed Cache Entries")
    print("=" * 50)
    
    # Get all cached entries
    cached_entries = get_all_cached_entries()
    print(f"Found {len(cached_entries)} total cached entries")
    
    single_count = 0
    multi_count = 0
    
    for i, entry in enumerate(cached_entries[:10]):  # Check first 10
        entry_type = entry.get('type', 'single')
        
        if entry_type == 'multi':
            multi_count += 1
            tickers = entry.get('tickers', [])
            ticker_display = entry.get('ticker_display', 'Unknown')
            print(f"  Multi {multi_count}: {ticker_display} ({len(tickers)} tickers, {entry['num_years']}y)")
            
            # Verify multi-ticker entry structure
            required_fields = ['tickers', 'ticker_display', 'num_years', 'start_month', 'start_day', 'end_month', 'end_day']
            missing_fields = [field for field in required_fields if field not in entry]
            if missing_fields:
                print(f"    ❌ Missing fields: {missing_fields}")
            else:
                print(f"    ✅ Complete multi-ticker entry")
        else:
            single_count += 1
            ticker = entry.get('ticker', 'Unknown')
            print(f"  Single {single_count}: {ticker} ({entry['num_years']}y)")
            
            # Verify single ticker entry structure
            required_fields = ['ticker', 'num_years', 'start_month', 'start_day', 'end_month', 'end_day']
            missing_fields = [field for field in required_fields if field not in entry]
            if missing_fields:
                print(f"    ❌ Missing fields: {missing_fields}")
            else:
                print(f"    ✅ Complete single ticker entry")
    
    print(f"\nSummary: {single_count} single ticker, {multi_count} multi-ticker entries")
    return True

def test_cache_button_display():
    """Test cache button display logic for both types"""
    print("\nTesting Cache Button Display Logic")
    print("=" * 50)
    
    cached_entries = get_all_cached_entries()
    
    for i, entry in enumerate(cached_entries[:5]):  # Test first 5 entries
        entry_type = entry.get('type', 'single')
        date_range = f"{entry['start_month']}/{entry['start_day']} - {entry['end_month']}/{entry['end_day']}"
        
        if entry_type == 'multi':
            # Multi-ticker button text
            ticker_count = len(entry['tickers'])
            button_text = f"🔄 {entry['ticker_display']} ({ticker_count} stocks, {entry['num_years']}y) {date_range}"
            help_text = f"Load cached multi-ticker analysis for {ticker_count} stocks with {entry['num_years']} years"
            print(f"  Multi Button: {button_text}")
            print(f"    Help: {help_text}")
        else:
            # Single ticker button text
            button_text = f"📊 {entry['ticker']} ({entry['num_years']}y) {date_range}"
            help_text = f"Load cached analysis for {entry['ticker']} with {entry['num_years']} years"
            print(f"  Single Button: {button_text}")
            print(f"    Help: {help_text}")
        
        print(f"    Button Key: cache_load_{i}_{entry['cache_key'][:8]}")
        print()
    
    return True

def main():
    """Run all multi-ticker cache tests"""
    print("Multi-Ticker Cache Functionality Test Suite")
    print("=" * 60)
    
    # Run tests
    cache_ops = test_multi_ticker_cache_operations()
    mixed_entries = test_mixed_cache_entries()
    button_display = test_cache_button_display()
    
    print("\n" + "=" * 60)
    if cache_ops and mixed_entries and button_display:
        print("✅ All Multi-Ticker Cache Tests Passed!")
    else:
        print("⚠️ Some Multi-Ticker Cache Tests Failed")
    print("=" * 60)
    
    print("\nMulti-Ticker Cache Features:")
    print("• Separate cache system for multi-ticker analyses")
    print("• Sorted ticker order for consistent cache keys")
    print("• Mixed cache display (single + multi-ticker entries)")
    print("• Distinct button styles for different cache types")
    print("• Complete data preservation for multi-ticker analyses")
    
    print("\nCache Button Styles:")
    print("• Single: '📊 AAPL (3y) 1/1 - 12/31'")
    print("• Multi: '🔄 AAPL, MSFT, GOOGL (3 stocks, 2y) 1/1 - 12/31'")
    print("• Truncated: '🔄 AAPL, MSFT, GOOGL... (5 stocks, 2y) 1/1 - 12/31'")
    
    print("\nCache Data Structure:")
    print("• Single: ticker, num_years, date_range, years_data")
    print("• Multi: tickers[], num_years, date_range, all_years_data{}")
    print("• Type field distinguishes between single and multi")
    print("• Backward compatibility with existing single caches")
    
    print("\nUser Experience:")
    print("• Click single cache → Detailed analysis with 5 charts")
    print("• Click multi cache → Comparison analysis with 2 charts")
    print("• Same loading experience for both cache types")
    print("• Automatic cache saving for both analysis types")

if __name__ == "__main__":
    main()
