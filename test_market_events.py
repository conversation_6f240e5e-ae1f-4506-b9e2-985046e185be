#!/usr/bin/env python3
"""
Test script to demonstrate the market events feature
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import get_ticker_specific_events

def test_market_events():
    """Test the ticker-specific market events function"""
    print("Testing Ticker-Specific Market Events Feature")
    print("=" * 60)

    # Test different tickers
    test_tickers = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'AMZN']
    test_years = [2024, 2023, 2022, 2021, 2020]

    for ticker in test_tickers:
        print(f"\n🏢 {ticker} Events:")
        print("-" * 40)
        for year in test_years:
            events = get_ticker_specific_events(ticker, year)
            print(f"\n📅 {year}: {events}")

        if ticker != test_tickers[-1]:  # Don't print separator after last ticker
            print("\n" + "=" * 60)

    # Test unknown ticker
    print(f"\n🏢 UNKNOWN Ticker Events:")
    print("-" * 40)
    events = get_ticker_specific_events("XYZ", 2023)
    print(f"\n📅 2023: {events}")
    
    print("\n" + "=" * 60)
    print("✓ Ticker-specific market events feature working correctly!")
    print("\nThis feature provides context for:")
    print("- Company-specific events and developments")
    print("- Understanding ticker-specific volatility patterns")
    print("- Explaining unusual seasonal behavior for specific stocks")
    print("- Connecting stock movements to company-specific events")
    print("- Better investment decision making with relevant context")
    print("- Automatic fallback to generic events for unknown tickers")

if __name__ == "__main__":
    test_market_events()
