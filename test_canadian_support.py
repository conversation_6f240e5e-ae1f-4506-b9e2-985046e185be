#!/usr/bin/env python3
"""
Test script for Canadian stock exchange support
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import normalize_canadian_ticker, try_canadian_variations, fetch_stock_data
from datetime import datetime

def test_canadian_ticker_normalization():
    """Test Canadian ticker normalization"""
    print("Testing Canadian Ticker Normalization")
    print("=" * 50)
    
    # Test cases
    test_cases = [
        # (input, expected_output)
        ('SHOP', 'SHOP.TO'),  # Known Canadian stock
        ('RY', 'RY.TO'),      # Royal Bank
        ('CNR', 'CNR.TO'),    # Canadian National Railway
        ('AAPL', 'AAPL'),     # US stock should remain unchanged
        ('MSFT', 'MSFT'),     # US stock should remain unchanged
        ('SHOP.TO', 'SHOP.TO'),  # Already has suffix
        ('TEST.V', 'TEST.V'),    # TSX Venture
        ('UNKNOWN', 'UNKNOWN'),  # Unknown ticker
    ]
    
    print("\n1. Testing ticker normalization...")
    for input_ticker, expected in test_cases:
        result = normalize_canadian_ticker(input_ticker)
        status = "✓" if result == expected else "✗"
        print(f"   {status} {input_ticker} → {result} (expected: {expected})")
    
    print("\n2. Testing Canadian variations...")
    test_ticker = "SHOP"
    variations = try_canadian_variations(test_ticker)
    print(f"   Variations for {test_ticker}: {variations}")
    
    expected_variations = ['SHOP', 'SHOP.TO', 'SHOP.V', 'SHOP.CN', 'SHOP.NE']
    if variations == expected_variations:
        print("   ✓ Variations generated correctly")
    else:
        print("   ✗ Variations incorrect")
    
    print("\n3. Testing with existing suffix...")
    test_ticker_with_suffix = "RY.TO"
    variations_with_suffix = try_canadian_variations(test_ticker_with_suffix)
    print(f"   Variations for {test_ticker_with_suffix}: {variations_with_suffix}")
    
    # Should strip suffix and generate variations
    expected_base_variations = ['RY', 'RY.TO', 'RY.V', 'RY.CN', 'RY.NE']
    if variations_with_suffix == expected_base_variations:
        print("   ✓ Suffix stripping works correctly")
    else:
        print("   ✗ Suffix stripping failed")

def test_canadian_data_fetching():
    """Test fetching data for Canadian stocks"""
    print("\n" + "=" * 50)
    print("Testing Canadian Stock Data Fetching")
    print("=" * 50)
    
    # Test Canadian stocks
    canadian_stocks = ['SHOP', 'RY', 'CNR']
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 12, 31)
    
    print(f"\nTesting data fetch for period: {start_date.date()} to {end_date.date()}")
    
    for ticker in canadian_stocks:
        print(f"\n   Testing {ticker}...")
        try:
            data = fetch_stock_data(ticker, start_date, end_date)
            if data is not None and not data.empty:
                print(f"   ✓ Successfully fetched {len(data)} days of data for {ticker}")
                print(f"     Date range: {data.index[0].date()} to {data.index[-1].date()}")
                print(f"     Columns: {list(data.columns)}")
                print(f"     Sample close price: ${data['Close'].iloc[0]:.2f}")
            else:
                print(f"   ⚠ No data returned for {ticker}")
        except Exception as e:
            print(f"   ✗ Error fetching {ticker}: {str(e)}")

def test_canadian_company_info():
    """Test company information for Canadian stocks"""
    print("\n" + "=" * 50)
    print("Testing Canadian Company Information")
    print("=" * 50)
    
    from app import get_company_info
    
    canadian_stocks = ['SHOP', 'RY', 'CNR']
    
    for ticker in canadian_stocks:
        print(f"\n   Testing company info for {ticker}...")
        try:
            info = get_company_info(ticker)
            if info:
                print(f"   ✓ Company: {info['name']}")
                print(f"     Industry: {info['industry']}")
                print(f"     Headquarters: {info['headquarters']}")
                print(f"     Market Cap: {info['market_cap']}")
            else:
                print(f"   ⚠ No company info found for {ticker}")
        except Exception as e:
            print(f"   ✗ Error getting company info for {ticker}: {str(e)}")

def main():
    """Run all Canadian support tests"""
    print("Canadian Stock Exchange Support Tests")
    print("=" * 60)
    
    test_canadian_ticker_normalization()
    test_canadian_data_fetching()
    test_canadian_company_info()
    
    print("\n" + "=" * 60)
    print("✅ Canadian Support Tests Complete!")
    print("=" * 60)
    
    print("\nSupported Canadian Exchanges:")
    print("• TSX (Toronto Stock Exchange) - .TO suffix")
    print("• TSX Venture Exchange - .V suffix")
    print("• Canadian Securities Exchange - .CN suffix")
    print("• NEO Exchange - .NE suffix")
    
    print("\nExample Canadian Stocks:")
    print("• SHOP (Shopify)")
    print("• RY (Royal Bank of Canada)")
    print("• CNR (Canadian National Railway)")
    print("• TD (Toronto-Dominion Bank)")
    print("• BMO (Bank of Montreal)")
    
    print("\nHow it works:")
    print("• Enter ticker without suffix (e.g., 'SHOP')")
    print("• App automatically tries Canadian exchanges")
    print("• Falls back to US markets if not found")
    print("• Supports manual suffixes (e.g., 'SHOP.TO')")

if __name__ == "__main__":
    main()
