#!/usr/bin/env python3
"""
Test script to demonstrate the gradient color scheme
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import generate_gradient_colors

def test_color_gradients():
    """Test the gradient color generation"""
    print("Testing Gradient Color Generation")
    print("=" * 50)
    
    # Test with different base colors and number of years
    test_cases = [
        ("#1f77b4", 5, "Blue (Price Chart)"),
        ("#2ca02c", 5, "Green (Return Chart)"),
        ("#d62728", 3, "Red (3 years)"),
        ("#ff7f0e", 7, "Orange (7 years)")
    ]
    
    for base_color, num_colors, description in test_cases:
        print(f"\n{description}:")
        print(f"Base color: {base_color}")
        print(f"Number of years: {num_colors}")
        
        colors = generate_gradient_colors(base_color, num_colors)
        
        print("Generated gradient (oldest → newest):")
        for i, color in enumerate(colors):
            year = 2024 - (num_colors - 1) + i  # Example years
            darkness = "██████████" if i == 0 else "█" * max(1, 10 - i*2) + "░" * min(9, i*2)
            print(f"  {year}: {color} {darkness}")
    
    print("\n" + "=" * 50)
    print("✓ Color gradient generation working correctly!")
    print("\nHow it works:")
    print("- Older years get darker, more saturated colors")
    print("- Newer years get lighter, less saturated colors")
    print("- This makes it easy to see the time progression in charts")

if __name__ == "__main__":
    test_color_gradients()
